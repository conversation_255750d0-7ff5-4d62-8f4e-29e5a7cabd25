<?php
// Test database connection and form functionality
require_once 'config/database.php';

echo "<h2>Database Connection Test</h2>";

// Test database connection
if (testConnection()) {
    echo "✅ Database connection successful!<br>";
    
    // Get database connection
    $pdo = getDBConnection();
    
    // Check if tables exist
    try {
        // Check contact_us table
        $stmt = $pdo->query("SHOW TABLES LIKE 'contact_us'");
        if ($stmt->rowCount() > 0) {
            echo "✅ contact_us table exists<br>";
            
            // Count records
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_us");
            $result = $stmt->fetch();
            echo "📊 Contact records: " . $result['count'] . "<br>";
        } else {
            echo "❌ contact_us table does not exist<br>";
        }
        
        // Check enquiry table
        $stmt = $pdo->query("SHOW TABLES LIKE 'enquiry'");
        if ($stmt->rowCount() > 0) {
            echo "✅ enquiry table exists<br>";
            
            // Count records
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM enquiry");
            $result = $stmt->fetch();
            echo "📊 Enquiry records: " . $result['count'] . "<br>";
        } else {
            echo "❌ enquiry table does not exist<br>";
        }
        
        // Check admin_users table
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
        if ($stmt->rowCount() > 0) {
            echo "✅ admin_users table exists<br>";
            
            // Count records
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
            $result = $stmt->fetch();
            echo "📊 Admin users: " . $result['count'] . "<br>";
        } else {
            echo "❌ admin_users table does not exist<br>";
        }
        
    } catch (PDOException $e) {
        echo "❌ Error checking tables: " . $e->getMessage() . "<br>";
    }
    
} else {
    echo "❌ Database connection failed!<br>";
    echo "Please make sure XAMPP MySQL service is running.<br>";
}

echo "<br><h2>Form Test</h2>";
echo "<p>Test the forms by submitting data through the website forms.</p>";
echo "<p>You can check the admin panel at: <a href='admin/'>admin/</a></p>";
echo "<p>Default login: username = admin, password = admin123</p>";

echo "<br><h2>PDF Files Check</h2>";
$pdfFiles = [
    'Godrej The Aqua Retreat 1BHK -588 Sq.ft.pdf',
    'Godrej The Aqua Retreat 2BHK -873 Sq.ft.pdf',
    'Godrej The Aqua Retreat 3BHK -1082 Sq.ft.pdf'
];

foreach ($pdfFiles as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists (" . round(filesize($file) / 1024, 2) . " KB)<br>";
    } else {
        echo "❌ $file not found<br>";
    }
}

echo "<br><h2>Quick Setup</h2>";
echo "<p>If tables don't exist, run: <a href='setup_database.php'>setup_database.php</a></p>";
?>
