<?php
// Database configuration for XAMPP
define('DB_HOST', 'localhost');
define('DB_PORT', '3306');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', ''); // Default XAMPP password is empty
define('DB_NAME', 'godrej_property');

// Create connection
function getDBConnection() {
    try {
        // Try standard TCP connection first
        $pdo = new PDO("mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                       DB_USERNAME,
                       DB_PASSWORD);

        // Set PDO attributes
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

        return $pdo;
    } catch(PDOException $e) {
        // Try socket connection as fallback
        try {
            $pdo = new PDO("mysql:unix_socket=/opt/lampp/var/mysql/mysql.sock;dbname=" . DB_NAME . ";charset=utf8mb4",
                           DB_USERNAME,
                           DB_PASSWORD);

            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

            return $pdo;
        } catch(PDOException $e2) {
            error_log("Database connection failed (both TCP and socket): " . $e->getMessage() . " | " . $e2->getMessage());
            return false;
        }
    }
}

// Function to create database if it doesn't exist
function createDatabaseIfNotExists() {
    try {
        $pdo = new PDO("mysql:unix_socket=" . DB_SOCKET . ";charset=utf8mb4",
                       DB_USERNAME,
                       DB_PASSWORD);

        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);

        return true;
    } catch(PDOException $e) {
        error_log("Database creation failed: " . $e->getMessage());
        return false;
    }
}

// Test database connection
function testConnection() {
    $connection = getDBConnection();
    if ($connection) {
        return true;
    }
    return false;
}
?>
