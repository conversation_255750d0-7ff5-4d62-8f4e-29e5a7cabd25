<?php
// Generate PDF thumbnail images
// This script creates professional PDF preview images

function createPDFThumbnail($title, $subtitle, $filename) {
    // Create a 300x400 image for better quality
    $width = 300;
    $height = 400;
    $image = imagecreatetruecolor($width, $height);

    // Enable antialiasing
    imageantialias($image, true);

    // Define colors
    $white = imagecolorallocate($image, 255, 255, 255);
    $lightgray = imagecolorallocate($image, 248, 249, 250);
    $gray = imagecolorallocate($image, 233, 236, 239);
    $darkgray = imagecolorallocate($image, 73, 80, 87);
    $gold = imagecolorallocate($image, 212, 175, 55);
    $border = imagecolorallocate($image, 206, 212, 218);
    $shadow = imagecolorallocate($image, 200, 200, 200);

    // Fill background
    imagefill($image, 0, 0, $lightgray);

    // Draw shadow effect
    imagefilledrectangle($image, 8, 8, $width-8, $height-8, $shadow);

    // Draw main document
    imagefilledrectangle($image, 0, 0, $width-16, $height-16, $white);
    imagerectangle($image, 0, 0, $width-16, $height-16, $border);

    // Draw header area
    imagefilledrectangle($image, 0, 0, $width-16, 60, $gold);

    // Add Godrej branding in header
    $headerText = "GODREJ PROPERTIES";
    $headerX = ($width - 16 - strlen($headerText) * 8) / 2;
    imagestring($image, 3, $headerX, 20, $headerText, $white);

    // Draw content area
    $contentX = 30;
    $contentY = 80;
    $contentWidth = $width - 76;
    $contentHeight = $height - 160;

    // Create floor plan layout based on BHK type
    $roomColor = imagecolorallocate($image, 245, 245, 245);
    $wallColor = imagecolorallocate($image, 100, 100, 100);

    if (strpos($filename, '1bhk') !== false) {
        // 1BHK Layout
        // Living room
        imagefilledrectangle($image, $contentX, $contentY, $contentX + 120, $contentY + 80, $roomColor);
        imagerectangle($image, $contentX, $contentY, $contentX + 120, $contentY + 80, $wallColor);
        imagestring($image, 2, $contentX + 30, $contentY + 35, "LIVING", $darkgray);

        // Bedroom
        imagefilledrectangle($image, $contentX + 130, $contentY, $contentX + 220, $contentY + 80, $roomColor);
        imagerectangle($image, $contentX + 130, $contentY, $contentX + 220, $contentY + 80, $wallColor);
        imagestring($image, 2, $contentX + 150, $contentY + 35, "BEDROOM", $darkgray);

        // Kitchen
        imagefilledrectangle($image, $contentX, $contentY + 90, $contentX + 80, $contentY + 140, $roomColor);
        imagerectangle($image, $contentX, $contentY + 90, $contentX + 80, $contentY + 140, $wallColor);
        imagestring($image, 2, $contentX + 15, $contentY + 110, "KITCHEN", $darkgray);

        // Bathroom
        imagefilledrectangle($image, $contentX + 90, $contentY + 90, $contentX + 140, $contentY + 140, $roomColor);
        imagerectangle($image, $contentX + 90, $contentY + 90, $contentX + 140, $contentY + 140, $wallColor);
        imagestring($image, 2, $contentX + 100, $contentY + 110, "BATH", $darkgray);

    } elseif (strpos($filename, '2bhk') !== false) {
        // 2BHK Layout
        // Living room
        imagefilledrectangle($image, $contentX, $contentY, $contentX + 140, $contentY + 70, $roomColor);
        imagerectangle($image, $contentX, $contentY, $contentX + 140, $contentY + 70, $wallColor);
        imagestring($image, 2, $contentX + 40, $contentY + 30, "LIVING ROOM", $darkgray);

        // Bedroom 1
        imagefilledrectangle($image, $contentX + 150, $contentY, $contentX + 220, $contentY + 70, $roomColor);
        imagerectangle($image, $contentX + 150, $contentY, $contentX + 220, $contentY + 70, $wallColor);
        imagestring($image, 2, $contentX + 155, $contentY + 30, "BED 1", $darkgray);

        // Bedroom 2
        imagefilledrectangle($image, $contentX, $contentY + 80, $contentX + 70, $contentY + 130, $roomColor);
        imagerectangle($image, $contentX, $contentY + 80, $contentX + 70, $contentY + 130, $wallColor);
        imagestring($image, 2, $contentX + 10, $contentY + 100, "BED 2", $darkgray);

        // Kitchen
        imagefilledrectangle($image, $contentX + 80, $contentY + 80, $contentX + 140, $contentY + 130, $roomColor);
        imagerectangle($image, $contentX + 80, $contentY + 80, $contentX + 140, $contentY + 130, $wallColor);
        imagestring($image, 2, $contentX + 90, $contentY + 100, "KITCHEN", $darkgray);

        // Bathrooms
        imagefilledrectangle($image, $contentX + 150, $contentY + 80, $contentX + 190, $contentY + 110, $roomColor);
        imagerectangle($image, $contentX + 150, $contentY + 80, $contentX + 190, $contentY + 110, $wallColor);
        imagestring($image, 1, $contentX + 155, $contentY + 90, "BATH1", $darkgray);

        imagefilledrectangle($image, $contentX + 150, $contentY + 120, $contentX + 190, $contentY + 150, $roomColor);
        imagerectangle($image, $contentX + 150, $contentY + 120, $contentX + 190, $contentY + 150, $wallColor);
        imagestring($image, 1, $contentX + 155, $contentY + 130, "BATH2", $darkgray);

    } else {
        // 3BHK Layout
        // Living room
        imagefilledrectangle($image, $contentX, $contentY, $contentX + 120, $contentY + 60, $roomColor);
        imagerectangle($image, $contentX, $contentY, $contentX + 120, $contentY + 60, $wallColor);
        imagestring($image, 2, $contentX + 30, $contentY + 25, "LIVING", $darkgray);

        // Bedrooms
        imagefilledrectangle($image, $contentX + 130, $contentY, $contentX + 190, $contentY + 60, $roomColor);
        imagerectangle($image, $contentX + 130, $contentY, $contentX + 190, $contentY + 60, $wallColor);
        imagestring($image, 1, $contentX + 140, $contentY + 25, "BED 1", $darkgray);

        imagefilledrectangle($image, $contentX, $contentY + 70, $contentX + 60, $contentY + 120, $roomColor);
        imagerectangle($image, $contentX, $contentY + 70, $contentX + 60, $contentY + 120, $wallColor);
        imagestring($image, 1, $contentX + 10, $contentY + 90, "BED 2", $darkgray);

        imagefilledrectangle($image, $contentX + 70, $contentY + 70, $contentX + 130, $contentY + 120, $roomColor);
        imagerectangle($image, $contentX + 70, $contentY + 70, $contentX + 130, $contentY + 120, $wallColor);
        imagestring($image, 1, $contentX + 80, $contentY + 90, "BED 3", $darkgray);

        // Kitchen & Dining
        imagefilledrectangle($image, $contentX + 140, $contentY + 70, $contentX + 200, $contentY + 100, $roomColor);
        imagerectangle($image, $contentX + 140, $contentY + 70, $contentX + 200, $contentY + 100, $wallColor);
        imagestring($image, 1, $contentX + 150, $contentY + 80, "KITCHEN", $darkgray);

        imagefilledrectangle($image, $contentX + 140, $contentY + 110, $contentX + 200, $contentY + 140, $roomColor);
        imagerectangle($image, $contentX + 140, $contentY + 110, $contentX + 200, $contentY + 140, $wallColor);
        imagestring($image, 1, $contentX + 150, $contentY + 120, "DINING", $darkgray);
    }

    // Add footer with title and area
    $footerY = $height - 80;
    imagefilledrectangle($image, 0, $footerY, $width-16, $height-16, $lightgray);

    // Title
    $titleX = ($width - 16 - strlen($title) * 12) / 2;
    imagestring($image, 4, $titleX, $footerY + 15, $title, $darkgray);

    // Subtitle
    $subtitleX = ($width - 16 - strlen($subtitle) * 10) / 2;
    imagestring($image, 3, $subtitleX, $footerY + 40, $subtitle, $gold);

    // Save image
    $filename_path = "images/pdf_thumbnail_$filename.png";
    imagepng($image, $filename_path);
    imagedestroy($image);

    return $filename_path;
}

// Create thumbnails for each BHK type
$thumbnails = [
    ['1BHK Layout', '588 Sq.ft', '1bhk'],
    ['2BHK Layout', '873 Sq.ft', '2bhk'],
    ['3BHK Layout', '1082 Sq.ft', '3bhk']
];

echo "<h2>Generating PDF Thumbnails</h2>";

// Create images directory if it doesn't exist
if (!is_dir('images')) {
    mkdir('images', 0755, true);
}

foreach ($thumbnails as $thumb) {
    $filename = createPDFThumbnail($thumb[0], $thumb[1], $thumb[2]);
    echo "✅ Created: $filename<br>";
}

echo "<br>✅ All thumbnails generated successfully!<br>";
echo "<p>You can now use these images in your layout plan section.</p>";
?>
