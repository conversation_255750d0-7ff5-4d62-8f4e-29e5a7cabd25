<?php
// Simple PDF thumbnail creator
echo "<h2>Creating PDF Thumbnails</h2>";

// Create images directory if it doesn't exist
if (!is_dir('images')) {
    mkdir('images', 0755, true);
}

// Create simple but professional thumbnails
function createSimpleThumbnail($bhk, $sqft, $filename) {
    $width = 300;
    $height = 400;
    
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Colors
    $white = imagecolorallocate($image, 255, 255, 255);
    $lightgray = imagecolorallocate($image, 248, 249, 250);
    $gray = imagecolorallocate($image, 108, 117, 125);
    $gold = imagecolorallocate($image, 212, 175, 55);
    $darkblue = imagecolorallocate($image, 33, 37, 41);
    $border = imagecolorallocate($image, 222, 226, 230);
    
    // Fill background
    imagefill($image, 0, 0, $white);
    
    // Draw border
    imagerectangle($image, 0, 0, $width-1, $height-1, $border);
    
    // Header section
    imagefilledrectangle($image, 0, 0, $width, 80, $gold);
    
    // Add title
    $title = "GODREJ PROPERTIES";
    imagestring($image, 3, ($width - strlen($title) * 10) / 2, 25, $title, $white);
    imagestring($image, 2, ($width - 12 * 8) / 2, 50, "Layout Plan", $white);
    
    // Main content area
    $contentY = 100;
    
    // BHK Type
    $bhkText = $bhk . " BHK";
    imagestring($image, 5, ($width - strlen($bhkText) * 15) / 2, $contentY + 20, $bhkText, $darkblue);
    
    // Area
    $areaText = $sqft . " Sq.ft";
    imagestring($image, 4, ($width - strlen($areaText) * 12) / 2, $contentY + 60, $areaText, $gray);
    
    // Simple floor plan representation
    $planY = $contentY + 100;
    $planX = 50;
    $planWidth = $width - 100;
    $planHeight = 150;
    
    // Draw plan background
    imagefilledrectangle($image, $planX, $planY, $planX + $planWidth, $planY + $planHeight, $lightgray);
    imagerectangle($image, $planX, $planY, $planX + $planWidth, $planY + $planHeight, $border);
    
    // Draw rooms based on BHK
    $roomColor = imagecolorallocate($image, 255, 255, 255);
    $roomBorder = imagecolorallocate($image, 200, 200, 200);
    
    if ($bhk == "1") {
        // 1BHK - Simple layout
        imagefilledrectangle($image, $planX + 10, $planY + 10, $planX + 90, $planY + 70, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 10, $planX + 90, $planY + 70, $roomBorder);
        imagestring($image, 1, $planX + 30, $planY + 35, "BEDROOM", $gray);
        
        imagefilledrectangle($image, $planX + 100, $planY + 10, $planX + 190, $planY + 70, $roomColor);
        imagerectangle($image, $planX + 100, $planY + 10, $planX + 190, $planY + 70, $roomBorder);
        imagestring($image, 1, $planX + 125, $planY + 35, "LIVING", $gray);
        
        imagefilledrectangle($image, $planX + 10, $planY + 80, $planX + 90, $planY + 130, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 80, $planX + 90, $planY + 130, $roomBorder);
        imagestring($image, 1, $planX + 25, $planY + 100, "KITCHEN", $gray);
        
    } elseif ($bhk == "2") {
        // 2BHK - More rooms
        imagefilledrectangle($image, $planX + 10, $planY + 10, $planX + 70, $planY + 60, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 10, $planX + 70, $planY + 60, $roomBorder);
        imagestring($image, 1, $planX + 20, $planY + 30, "BED 1", $gray);
        
        imagefilledrectangle($image, $planX + 80, $planY + 10, $planX + 140, $planY + 60, $roomColor);
        imagerectangle($image, $planX + 80, $planY + 10, $planX + 140, $planY + 60, $roomBorder);
        imagestring($image, 1, $planX + 90, $planY + 30, "BED 2", $gray);
        
        imagefilledrectangle($image, $planX + 150, $planY + 10, $planX + 190, $planY + 60, $roomColor);
        imagerectangle($image, $planX + 150, $planY + 10, $planX + 190, $planY + 60, $roomBorder);
        imagestring($image, 1, $planX + 155, $planY + 30, "BATH", $gray);
        
        imagefilledrectangle($image, $planX + 10, $planY + 70, $planX + 120, $planY + 130, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 70, $planX + 120, $planY + 130, $roomBorder);
        imagestring($image, 1, $planX + 40, $planY + 95, "LIVING", $gray);
        
        imagefilledrectangle($image, $planX + 130, $planY + 70, $planX + 190, $planY + 130, $roomColor);
        imagerectangle($image, $planX + 130, $planY + 70, $planX + 190, $planY + 130, $roomBorder);
        imagestring($image, 1, $planX + 140, $planY + 95, "KITCHEN", $gray);
        
    } else {
        // 3BHK - Most rooms
        imagefilledrectangle($image, $planX + 10, $planY + 10, $planX + 60, $planY + 50, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 10, $planX + 60, $planY + 50, $roomBorder);
        imagestring($image, 1, $planX + 15, $planY + 25, "BED 1", $gray);
        
        imagefilledrectangle($image, $planX + 70, $planY + 10, $planX + 120, $planY + 50, $roomColor);
        imagerectangle($image, $planX + 70, $planY + 10, $planX + 120, $planY + 50, $roomBorder);
        imagestring($image, 1, $planX + 75, $planY + 25, "BED 2", $gray);
        
        imagefilledrectangle($image, $planX + 130, $planY + 10, $planX + 180, $planY + 50, $roomColor);
        imagerectangle($image, $planX + 130, $planY + 10, $planX + 180, $planY + 50, $roomBorder);
        imagestring($image, 1, $planX + 135, $planY + 25, "BED 3", $gray);
        
        imagefilledrectangle($image, $planX + 10, $planY + 60, $planX + 100, $planY + 110, $roomColor);
        imagerectangle($image, $planX + 10, $planY + 60, $planX + 100, $planY + 110, $roomBorder);
        imagestring($image, 1, $planX + 35, $planY + 80, "LIVING", $gray);
        
        imagefilledrectangle($image, $planX + 110, $planY + 60, $planX + 160, $planY + 110, $roomColor);
        imagerectangle($image, $planX + 110, $planY + 60, $planX + 160, $planY + 110, $roomBorder);
        imagestring($image, 1, $planX + 120, $planY + 80, "KITCHEN", $gray);
        
        imagefilledrectangle($image, $planX + 170, $planY + 60, $planX + 190, $planY + 110, $roomColor);
        imagerectangle($image, $planX + 170, $planY + 60, $planX + 190, $planY + 110, $roomBorder);
        imagestring($image, 1, $planX + 172, $planY + 80, "BATH", $gray);
    }
    
    // Footer
    $footerY = $height - 60;
    imagestring($image, 2, ($width - 16 * 8) / 2, $footerY, "Download PDF Plan", $gold);
    
    // Save
    $filepath = "images/pdf_thumbnail_" . $filename . ".png";
    imagepng($image, $filepath);
    imagedestroy($image);
    
    return $filepath;
}

// Create thumbnails
$thumbnails = [
    ['1', '588', '1bhk'],
    ['2', '873', '2bhk'],
    ['3', '1082', '3bhk']
];

foreach ($thumbnails as $thumb) {
    $file = createSimpleThumbnail($thumb[0], $thumb[1], $thumb[2]);
    echo "✅ Created: $file<br>";
}

echo "<br>✅ All thumbnails created successfully!";
echo "<br><a href='index.html#plans'>View Layout Plans</a>";
?>
