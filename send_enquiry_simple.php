<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

require_once 'config/database.php';

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Validate required fields
    if (empty($data['name']) || empty($data['phone']) || empty($data['email'])) {
        echo json_encode(['success' => false, 'message' => 'Please fill in all required fields']);
        exit;
    }

    // Sanitize and validate data
    $name = trim($data['name']);
    $phone = trim($data['phone']);
    $email = trim($data['email']);
    $message = isset($data['message']) ? trim($data['message']) : '';
    $updates = isset($data['updates']) ? ($data['updates'] ? 1 : 0) : 0;

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
        exit;
    }

    // Validate phone number (10 digits for Indian numbers)
    if (!preg_match('/^[0-9]{10}$/', $phone)) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid 10-digit mobile number']);
        exit;
    }

    // Get database connection
    $pdo = getDBConnection();
    if (!$pdo) {
        // Fallback to file if database fails
        $logEntry = date('Y-m-d H:i:s') . " - Enquiry from: $name ($email, $phone) [DB FAILED]\n";
        file_put_contents('enquiries.log', $logEntry, FILE_APPEND | LOCK_EX);
        echo json_encode(['success' => false, 'message' => 'Database connection failed. Please try again later.']);
        exit;
    }

    // Check if email or phone already exists
    $checkSQL = "SELECT id FROM enquiry WHERE email = ? OR phone = ? ORDER BY created_at DESC LIMIT 1";
    $checkStmt = $pdo->prepare($checkSQL);
    $checkStmt->execute([$email, $phone]);

    if ($checkStmt->rowCount() > 0) {
        // Update existing record instead of creating duplicate
        $existingRecord = $checkStmt->fetch();
        $updateSQL = "UPDATE enquiry SET name = ?, message = ?, updates = ?, status = 'new', updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $updateStmt = $pdo->prepare($updateSQL);
        $updateStmt->execute([$name, $message, $updates, $existingRecord['id']]);

        echo json_encode([
            'success' => true,
            'message' => 'Your enquiry has been updated successfully!',
            'saved' => true,
            'debug' => 'Updated existing record in database'
        ]);
    } else {
        // Insert new enquiry
        $insertSQL = "INSERT INTO enquiry (name, phone, email, message, updates) VALUES (?, ?, ?, ?, ?)";
        $insertStmt = $pdo->prepare($insertSQL);
        $insertStmt->execute([$name, $phone, $email, $message, $updates]);

        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your enquiry! Our team will contact you shortly.',
            'saved' => true,
            'debug' => 'New record saved to database'
        ]);
    }

} catch (Exception $e) {
    error_log("Error in enquiry form: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Sorry, there was an error processing your enquiry']);
}
?>
