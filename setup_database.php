<?php
require_once 'config/database.php';

// Create database if it doesn't exist
if (!createDatabaseIfNotExists()) {
    die("Failed to create database. Please check your XAMPP MySQL service is running.");
}

// Get database connection
$pdo = getDBConnection();
if (!$pdo) {
    die("Failed to connect to database. Please check your XAMPP MySQL service is running.");
}

try {
    // Create contact_us table
    $contactTableSQL = "
    CREATE TABLE IF NOT EXISTS contact_us (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        country VARCHAR(100) DEFAULT 'India',
        updates BOOLEAN DEFAULT FALSE,
        status ENUM('new', 'contacted', 'follow_up', 'closed') DEFAULT 'new',
        follow_up_date DATE NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_email (email),
        INDEX idx_phone (phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($contactTableSQL);
    echo "✓ Contact Us table created successfully<br>";

    // Create enquiry table
    $enquiryTableSQL = "
    CREATE TABLE IF NOT EXISTS enquiry (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(255) NOT NULL,
        message TEXT NULL,
        updates BOOLEAN DEFAULT FALSE,
        status ENUM('new', 'contacted', 'follow_up', 'closed') DEFAULT 'new',
        follow_up_date DATE NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_email (email),
        INDEX idx_phone (phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($enquiryTableSQL);
    echo "✓ Enquiry table created successfully<br>";

    // Create admin users table
    $adminTableSQL = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($adminTableSQL);
    echo "✓ Admin users table created successfully<br>";

    // Create default admin user (username: admin, password: admin123)
    $defaultAdminSQL = "
    INSERT IGNORE INTO admin_users (username, password, email, full_name) 
    VALUES ('admin', ?, '<EMAIL>', 'Administrator')";
    
    $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare($defaultAdminSQL);
    $stmt->execute([$hashedPassword]);
    
    if ($stmt->rowCount() > 0) {
        echo "✓ Default admin user created (username: admin, password: admin123)<br>";
    } else {
        echo "✓ Admin user already exists<br>";
    }

    echo "<br><strong>Database setup completed successfully!</strong><br>";
    echo "<br>You can now:<br>";
    echo "1. Test your contact forms<br>";
    echo "2. Access admin panel at: <a href='admin/'>admin/</a><br>";
    echo "3. Login with username: <strong>admin</strong> and password: <strong>admin123</strong><br>";

} catch(PDOException $e) {
    echo "Error creating tables: " . $e->getMessage();
}
?>
