<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Forms - Godrej Property</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #d4af37;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #b8941f;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Form Testing - Godrej Property</h1>
    
    <div class="test-section">
        <h2>Test Enquiry Form</h2>
        <form id="testEnquiryForm">
            <div class="form-group">
                <label for="enquiryName">Name*</label>
                <input type="text" id="enquiryName" name="name" required value="Test User">
            </div>
            <div class="form-group">
                <label for="enquiryPhone">Phone*</label>
                <input type="tel" id="enquiryPhone" name="phone" required value="9876543210">
            </div>
            <div class="form-group">
                <label for="enquiryEmail">Email*</label>
                <input type="email" id="enquiryEmail" name="email" required value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="enquiryMessage">Message</label>
                <textarea id="enquiryMessage" name="message" rows="3">Test enquiry message</textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="updates" checked> I agree to receive updates
                </label>
            </div>
            <button type="submit">Test Enquiry Form</button>
            <div id="enquiryResult" class="result"></div>
        </form>
    </div>

    <div class="test-section">
        <h2>Test Contact Form</h2>
        <form id="testContactForm">
            <div class="form-group">
                <label for="contactName">Name*</label>
                <input type="text" id="contactName" name="name" required value="Test Contact">
            </div>
            <div class="form-group">
                <label for="contactPhone">Phone*</label>
                <input type="tel" id="contactPhone" name="phone" required value="9876543210">
            </div>
            <div class="form-group">
                <label for="contactEmail">Email*</label>
                <input type="email" id="contactEmail" name="email" required value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="contactCountry">Country</label>
                <select id="contactCountry" name="country">
                    <option value="India" selected>India</option>
                    <option value="USA">USA</option>
                    <option value="UK">UK</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="updates" checked> I agree to receive updates
                </label>
            </div>
            <button type="submit">Test Contact Form</button>
            <div id="contactResult" class="result"></div>
        </form>
    </div>

    <div class="test-section">
        <h2>Database Status</h2>
        <button onclick="checkDatabase()">Check Database Status</button>
        <div id="databaseResult" class="result"></div>
    </div>

    <script>
        // Test Enquiry Form
        document.getElementById('testEnquiryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                message: formData.get('message'),
                updates: formData.get('updates') ? true : false
            };
            
            const resultDiv = document.getElementById('enquiryResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Sending...';
            
            fetch('send_enquiry_simple.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ ' + data.message;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ ' + data.message;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Error: ' + error.message;
            });
        });

        // Test Contact Form
        document.getElementById('testContactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                country: formData.get('country'),
                updates: formData.get('updates') ? true : false
            };
            
            const resultDiv = document.getElementById('contactResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Sending...';
            
            fetch('send_contact_simple.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ ' + data.message;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ ' + data.message;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Error: ' + error.message;
            });
        });

        // Check Database Status
        function checkDatabase() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Checking...';
            
            fetch('test_database.php')
            .then(response => response.text())
            .then(data => {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = data;
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Error checking database: ' + error.message;
            });
        }
    </script>
</body>
</html>
