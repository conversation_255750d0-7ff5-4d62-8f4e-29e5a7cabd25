/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Prevent horizontal overflow */
*, *::before, *::after {
    max-width: 100%;
}

html {
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    background-attachment: fixed;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px; /* Increased from 1200px for wider layout */
    margin: 0 auto;
    padding: 0 30px;
    width: 100%;
    box-sizing: border-box;
}



/* Sub Navigation */
.sub-nav {
    background: rgba(45, 45, 45, 0.75);
    padding: 12px 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sub-nav.scrolled {
    background: rgba(45, 45, 45, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
}

.sub-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sub-nav-left {
    display: flex;
    align-items: center;
}

.godrej-logo {
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.godrej-logo img {
    height: 35px;
    width: auto;
}

.godrej-logo:hover {
    opacity: 0.8;
}

.sub-nav-menu {
    display: flex;
    list-style: none;
    gap: 10px;
    margin-left: auto;
}

.sub-nav-menu a {
    text-decoration: none;
    color: #ffffff !important;
    font-weight: 600;
    padding: 8px 15px;
    font-size: 13px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sub-nav-menu a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
}

.sub-nav.scrolled .sub-nav-menu a {
    color: #ffffff !important;
}

.sub-nav-menu a:visited,
.sub-nav-menu a:active {
    color: #ffffff !important;
}

/* Active menu item */
.sub-nav-menu a.active {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff !important;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    overflow: hidden;
    margin-top: 0;
}

.hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.6));
    color: white;
    padding: 80px 0 60px;
}

.hero-content {
    position: relative;
}

.hero-content h1 {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.location {
    font-size: 0.9rem;
    margin-bottom: 30px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.property-details {
    display: flex;
    align-items: center;
    gap: 25px;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.property-details span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.separator {
    color: #fff;
    font-weight: normal;
    opacity: 0.6;
}





/* Schedule Visit Button */
.schedule-visit-btn {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: #fff;
    color: #333;
    padding: 20px 15px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 600;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: all 0.3s ease;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.schedule-visit-btn:hover {
    background: #f0f0f0;
    transform: translateY(-50%) scale(1.05);
}

/* Call Button */
.call-btn {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(80px); /* Position below schedule visit button */
    background: #9fd325;
    color: white;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.call-btn:hover {
    background: #128c7e;
    transform: translateY(80px) scale(1.1);
    color: white;
    text-decoration: none;
    box-shadow: 0 6px 25px rgba(0,0,0,0.3);
}

.action-buttons {
    display: none; /* Hide the old action buttons */
}

.action-btn {
    padding: 12px 20px;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s;
    display: inline-block;
}

.action-btn:hover {
    background: rgba(255,255,255,0.3);
}



/* Section Styles */
section {
    padding: 20px 0;
}

section h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 15px;
    text-align: center;
}

/* Overview Section */
.overview {
    padding: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.95) 100%);
    backdrop-filter: blur(10px);
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.6);
}

.overview .container {
    padding: 0px 30px 10px;
}

.overview-header {
    text-align: center;
    margin-bottom: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-header h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    color: #2c3e50;
    margin: 0;
    padding: 0 40px;
    position: relative;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.overview-header h2:before,
.overview-header h2:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, #d4af37, #f1c40f);
    border-radius: 1px;
    box-shadow: 0 1px 3px rgba(212,175,55,0.3);
}

.overview-header h2:before {
    right: 100%;
    margin-right: 40px;
}

.overview-header h2:after {
    left: 100%;
    margin-left: 40px;
}

/* Shared header styles for all sections */
.plans-header,
.price-header,
.amenities-header,
.gallery-header,
.downloads-header,
.contact-header,
.certificates-header,
.neighbourhood-header {
    text-align: center;
    margin-bottom: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.plans-header h2,
.price-header h2,
.amenities-header h2,
.gallery-header h2,
.downloads-header h2,
.contact-header h2,
.certificates-header h2,
.neighbourhood-header h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 3px;
    color: #2c3e50;
    margin: 0;
    padding: 0 40px;
    position: relative;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.plans-header h2:before,
.plans-header h2:after,
.price-header h2:before,
.price-header h2:after,
.amenities-header h2:before,
.amenities-header h2:after,
.gallery-header h2:before,
.gallery-header h2:after,
.downloads-header h2:before,
.downloads-header h2:after,
.contact-header h2:before,
.contact-header h2:after,
.certificates-header h2:before,
.certificates-header h2:after,
.neighbourhood-header h2:before,
.neighbourhood-header h2:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, #d4af37, #f1c40f);
    border-radius: 1px;
    box-shadow: 0 1px 3px rgba(212,175,55,0.3);
}

.plans-header h2:before,
.price-header h2:before,
.amenities-header h2:before,
.gallery-header h2:before,
.downloads-header h2:before,
.contact-header h2:before,
.certificates-header h2:before,
.neighbourhood-header h2:before {
    right: 100%;
    margin-right: 40px;
}

.plans-header h2:after,
.price-header h2:after,
.amenities-header h2:after,
.gallery-header h2:after,
.downloads-header h2:after,
.contact-header h2:after,
.certificates-header h2:after,
.neighbourhood-header h2:after {
    left: 100%;
    margin-left: 40px;
}

.overview-content {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.overview-text h3 {
    font-size: 1.1rem;
    margin-bottom: 30px;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.overview-text p {
    font-size: 0.9rem;
    color: #555;
    line-height: 1.6;
    margin: 0;
    text-align: justify;
}

.overview-image {
    width: 100%;
    margin-top: 0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.overview-image img {
    width: 100%;
    height: 550px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.overview-image:hover img {
    transform: scale(1.02);
}

/* Hero Intro Section */
.hero-intro {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 50%, rgba(241,243,244,0.95) 100%);
    backdrop-filter: blur(10px);
    padding: 30px 0;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), 0 5px 15px rgba(0,0,0,0.08);
}

.hero-intro-content h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-intro-content h2 {
    font-size: 1.3rem;
    font-weight: 500;
    color: #3498db;
    margin-bottom: 8px;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.hero-tagline {
    font-size: 0.95rem;
    color: #7f8c8d;
    margin-bottom: 15px;
    font-style: italic;
    font-weight: 300;
}

.hero-description {
    margin-bottom: 20px;
}

.hero-description p {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #34495e;
    margin-bottom: 12px;
    text-align: justify;
}

.hero-cta-buttons {
    display: flex;
    gap: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.cta-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 200px;
    min-height: 56px;
    text-align: center;
    box-sizing: border-box;
}

.cta-btn.primary {
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    color: white;
    border: none;
}

.cta-btn.primary:hover {
    background: linear-gradient(135deg, #357ABD, #2E6DA4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.cta-btn.secondary {
    background: linear-gradient(135deg, #6C7B7F, #5A6C70);
    color: white;
    border: none;
}

.cta-btn.secondary:hover {
    background: linear-gradient(135deg, #5A6C70, #4A5B5F);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 123, 127, 0.3);
}

.cta-btn.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
}

.cta-btn.whatsapp:hover {
    background: linear-gradient(135deg, #128c7e, #075e54);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
}

.cta-btn.outline {
    background: linear-gradient(135deg, #2E86AB, #2471A3);
    color: white;
    border: 2px solid #2E86AB;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.cta-btn.outline:hover {
    background: linear-gradient(135deg, #2471A3, #1F618D);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 134, 171, 0.3);
    border-color: #2471A3;
}

/* Special premium styling for BOOK YOUR UNIT button */
.cta-btn.outline {
    position: relative;
    background: linear-gradient(135deg, #28A745, #20A039);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.25);
}

.cta-btn.outline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    border-radius: inherit;
    pointer-events: none;
}

/* Button styles for schedule visit buttons */
button.cta-btn,
button.cta-card {
    border: none;
    cursor: pointer;
    text-decoration: none;
    background: inherit;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    text-align: inherit;
}

button.cta-card {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Key Highlights Section */
.key-highlights {
    background: white;
    padding: 25px 0;
}

.highlights-header h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 5px;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.highlight-item {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.highlight-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    background: white;
}

.highlight-icon {
    font-size: 2rem;
    margin-bottom: 15px;
    display: block;
}

.highlight-item h4 {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    line-height: 1.4;
}

.highlight-item.aqua-zones ul {
    list-style: none;
    padding: 0;
    margin-top: 15px;
}

.highlight-item.aqua-zones li {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-bottom: 8px;
    text-align: left;
}

.highlights-cta {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Residences Section */
.residences-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 50px 0;
    color: white;
}

.residences-header {
    text-align: center;
    margin-bottom: 5px;
}

.residences-header h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 15px;
}

.residences-header h3 {
    font-size: 1rem;
    font-weight: 400;
    opacity: 0.9;
}

.residences-details {
    max-width: 1400px; /* Match the main container width */
    margin: 0 auto;
    text-align: center;
}

.residence-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* Force exactly 3 columns for single line */
    gap: 30px;
    margin-bottom: 40px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.info-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.info-label {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
    opacity: 0.8;
}

.info-value {
    display: block;
    font-size: 1rem;
    font-weight: 500;
}

.residence-description {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 40px;
    opacity: 0.9;
}

.residences-cta {
    display: flex;
    gap: 25px;
    justify-content: center;
    flex-wrap: nowrap; /* Changed from wrap to nowrap for single row */
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.residences-cta .cta-btn {
    flex: 1;
    min-width: 200px;
    max-width: 280px;
    text-align: center;
    white-space: nowrap;
    font-weight: 600;
    font-size: 1rem;
    padding: 16px 32px;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 56px;
    justify-content: center;
}

/* Location Advantage Section */
.location-advantage {
    background: #f8f9fa;
    padding: 50px 0;
}

.location-header {
    text-align: center;
    margin-bottom: 5px;
}

.location-header h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.location-header p {
    font-size: 1.3rem;
    color: #7f8c8d;
}

.location-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.location-card {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.location-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.location-card h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.location-card ul {
    list-style: none;
    padding: 0;
}

.location-card li {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 12px;
    padding-left: 10px;
}

.location-card p {
    font-size: 1.1rem;
    color: #7f8c8d;
    line-height: 1.6;
}

.location-cta {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Trust Section */
.trust-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 50px 0;
    color: white;
}

.trust-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.trust-content h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 30px;
}

.trust-content p {
    font-size: 0.9rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Final CTA Section */
.final-cta-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 50px 0;
    color: white;
}

.final-cta-content {
    text-align: center;
}

.final-cta-content h2 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 35px;
}

.cta-grid {
    display: flex; /* Changed from grid to flex for better control */
    justify-content: center;
    align-items: stretch;
    gap: 30px;
    margin-bottom: 50px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    flex-wrap: nowrap; /* Ensure single row */
}

.cta-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px 25px;
    border-radius: 12px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 160px;
    height: 100%;
    flex: 1; /* Make cards equal width */
    min-width: 250px;
    max-width: 300px;
}

.cta-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.cta-icon {
    font-size: 1.8rem;
    margin-bottom: 15px;
    display: block;
}

.cta-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.cta-card p {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

.urgency-banner {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.urgency-banner p {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.urgency-banner p:last-child {
    margin-bottom: 0;
}

/* Neighbourhood Section */
.neighbourhood {
    background: #f8f9fa;
    padding: 60px 0;
}

.neighbourhood-header {
    text-align: center;
    margin-bottom: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.neighbourhood-header h2 {
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 2px;
    color: #333;
    margin: 0;
    padding: 0 30px;
    position: relative;
    white-space: nowrap;
}

.neighbourhood-header h2:before,
.neighbourhood-header h2:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 60px;
    height: 1px;
    background: #d4af37;
}

.neighbourhood-header h2:before {
    right: 100%;
    margin-right: 30px;
}

.neighbourhood-header h2:after {
    left: 100%;
    margin-left: 30px;
}

.section-desc {
    text-align: center;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.map-container {
    position: relative;
    margin: 40px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    background: white;
}

/* Google Map */
#google-map {
    height: 450px;
    width: 100%;
    border-radius: 12px;
    border: none;
    display: block;
}

/* Fullscreen button */
.fullscreen-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: white;
    color: #333;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.fullscreen-btn:hover {
    background: #f8f9fa;
}

.fullscreen-btn .fullscreen-icon {
    font-size: 1.4rem;
    line-height: 1;
    animation: none !important;
}

/* Location highlights overlay - Single merged card */
.location-highlights {
    position: absolute;
    left: 20px;
    top: 20px;
    z-index: 10;
    max-width: 280px;
}

.location-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
    position: relative;
}

.location-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;
}



.location-header-icon {
    width: 24px;
    height: 24px;
    background: #4285f4;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.location-header-text {
    font-weight: 600;
    color: #333;
    font-size: 16px;
    flex: 1;
}

.location-header-left {
    font-weight: 600;
    color: #4285f4;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-direction-icon {
    width: 32px;
    height: 32px;
    background: #4285f4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.location-direction-icon:hover {
    background: #3367d6;
}

.location-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px 0;
}

.location-item:last-child {
    margin-bottom: 0;
}

.location-icon {
    width: 32px;
    height: 32px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    margin-top: 2px;
}

.location-item p {
    font-weight: 400;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

.source-note {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

/* Hide Google Maps "View larger map" link and other overlays */
.map-container iframe {
    pointer-events: auto;
}

.map-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: white;
    z-index: 5;
}

/* Plans Section */
.plans {
    background: #f8f9fa;
    padding: 50px 0;
}

.plans-header h2 {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.plans-content {
    max-width: 1200px;
    margin: 0 auto;
}

.plan-title {
    text-align: center;
    margin-bottom: 30px;
}

.plan-title h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.plan-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.plan-image-wrapper {
    flex: 1;
    position: relative;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    max-height: 600px;
}

.plan-image-wrapper:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.master-plan-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: transform 0.3s ease;
    transform-origin: center;
}

.plan-image-wrapper:hover .master-plan-image {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    filter: brightness(1.1);
}

.fullscreen-interact-btn {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(212, 175, 55, 0.95);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 15;
    transition: all 0.3s ease;
    opacity: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.plan-image-wrapper:hover .fullscreen-interact-btn {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    background: rgba(212, 175, 55, 1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.fullscreen-interact-btn:hover {
    transform: translateY(-50%) scale(1.15);
    background: rgba(212, 175, 55, 1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.fullscreen-icon {
    font-size: 26px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: openClose 2s ease-in-out infinite;
}

@keyframes openClose {
    0% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(0.8) rotate(90deg);
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    75% {
        transform: scale(0.9) rotate(270deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
    }
}

.interact-tooltip {
    position: absolute;
    left: 50%;
    bottom: 75px;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 20;
    min-width: 140px;
    text-align: center;
}

.interact-tooltip::before {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -6px;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(0, 0, 0, 0.9);
}

.fullscreen-interact-btn:hover .interact-tooltip {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 15;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 8px;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.zoom-btn:hover {
    background: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.zoom-btn:active {
    transform: scale(0.95);
}

/* Disable hover effects when zoomed */
.plan-image-wrapper.zoomed:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.plan-image-wrapper.zoomed:hover .master-plan-image {
    transform: none;
    box-shadow: none;
    filter: none;
}

.plan-image-wrapper.zoomed .hover-tooltip {
    display: none;
}

/* Interactive hotspots */
.plan-hotspot {
    position: absolute;
    cursor: pointer;
    z-index: 10;
}

.hotspot-marker {
    width: 12px;
    height: 12px;
    background: #d4af37;
    border: 3px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.hotspot-label {
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.plan-hotspot:hover .hotspot-label {
    opacity: 1;
}

/* Zoom controls */
.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 15;
}

.zoom-btn {
    width: 35px;
    height: 35px;
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.zoom-btn:hover {
    background: rgba(0,0,0,0.9);
}

/* Movement Controls */
.movement-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: none;
    flex-direction: column;
    gap: 5px;
    z-index: 10;
}

.move-btn {
    width: 40px;
    height: 40px;
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.move-btn:hover {
    background: rgba(0,0,0,0.9);
    transform: scale(1.1);
}

/* Explore More Section */
.explore-more-section {
    flex: 0 0 320px;
    padding: 30px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    height: fit-content;
}

.explore-more-section h3 {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 25px;
    letter-spacing: 1px;
}

.explore-more-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.explore-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.explore-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.explore-item:hover::before {
    transform: translateX(100%);
}

.explore-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.explore-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.explore-item:first-child .explore-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

.explore-item:last-child .explore-icon {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    box-shadow: 0 8px 20px rgba(255, 0, 0, 0.4);
}

.explore-icon i {
    font-size: 24px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.explore-item:hover .explore-icon i {
    transform: rotate(360deg) scale(1.2);
    text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
}

.explore-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.explore-content p {
    font-size: 0.85rem;
    color: #666;
    margin: 0;
    line-height: 1.3;
}



/* Side panel */
.plan-details-panel {
    flex: 0 0 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* PDF Thumbnails Grid */
.pdf-thumbnails-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
    padding: 0 20px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.pdf-thumbnail-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.pdf-thumbnail-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.pdf-thumbnail {
    position: relative;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    height: 250px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pdf-preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.pdf-preview-embed {
    width: 120%;
    height: 120%;
    border: none;
    pointer-events: none;
    transform: scale(0.85);
    transform-origin: top left;
    position: absolute;
    top: -10%;
    left: -10%;
}

.pdf-blank-preview {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    position: relative;
}

.pdf-blank-preview .pdf-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.pdf-blank-preview .pdf-label {
    font-size: 1rem;
    color: #495057;
    font-weight: 600;
    text-align: center;
}

.pdf-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.pdf-thumbnail:hover .pdf-overlay {
    opacity: 1;
}

.pdf-overlay .pdf-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.pdf-overlay .pdf-label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    text-align: center;
}

.pdf-info h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.pdf-info p {
    color: #666;
    margin: 0 0 15px 0;
    font-size: 0.9rem;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #d4af37, #b8941f);
    color: white;
    text-decoration: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(212, 175, 55, 0.3);
    min-width: 200px;
    min-height: 56px;
    text-align: center;
    box-sizing: border-box;
}

.download-btn:hover {
    background: linear-gradient(135deg, #b8941f, #d4af37);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
    color: white;
}

.download-btn:active {
    transform: translateY(0);
}

/* Ensure button elements have same styling as anchor elements */
button.download-btn {
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.download-icon {
    font-size: 1.1rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

/* Hover effect for PDF thumbnail */
.pdf-thumbnail-item:hover .pdf-thumbnail {
    border-color: #d4af37;
    background: #fff;
}

.pdf-thumbnail-item:hover .pdf-icon {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.scroll-to-top {
    position: sticky;
    top: 50%;
}

.scroll-top-btn {
    background: white;
    border: 1px solid #ddd;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-size: 10px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.scroll-top-btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.scroll-top-btn svg {
    color: #d4af37;
}

/* Certificates Section */
.certificates {
    background: #f8f9fa;
    padding: 40px 0;
}

.certificates-header {
    text-align: center;
    margin-bottom: 5px;
}

.certificates-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.certificates-container {
    max-width: 800px;
    margin: 0 auto;
}

.certificate-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.certificate-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.certificate-thumbnail {
    position: relative;
    width: 100%;
    height: 250px;
    overflow: hidden;
    background: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.certificate-preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.certificate-preview-embed {
    width: 120%;
    height: 120%;
    border: none;
    pointer-events: none;
    transform: scale(0.85);
    transform-origin: top left;
    position: absolute;
    top: -10%;
    left: -10%;
}

.certificate-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.certificate-thumbnail:hover .certificate-overlay {
    opacity: 1;
}

.certificate-overlay .certificate-icon {
    font-size: 2rem;
    margin-bottom: 8px;
    color: white;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.certificate-overlay .certificate-label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5);
    text-align: center;
}

.certificate-info {
    padding: 25px;
    text-align: center;
}

.certificate-info h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.certificate-info p {
    color: #666;
    margin: 0 0 20px 0;
    font-size: 0.9rem;
}

/* Price Section */
.price {
    /* background: #f8f9fa; */
    padding: 40px 0;
}

.price-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 40px;
}

/* Price Table Section */
.price-table-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.price-table-header {
    background: #d4af37;
    padding: 15px 20px;
    text-align: center;
}

.price-table-header h3 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.price-table {
    padding: 0;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.price-row:last-child {
    border-bottom: none;
}

.bhk-type {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
}

.price-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    filter: blur(3px);
    transition: filter 0.3s ease;
    cursor: pointer;
    user-select: none;
}

.price-value:hover {
    filter: blur(0px);
}

.price-value .onwards {
    font-size: 0.9rem;
    color: #666;
    font-weight: normal;
}

.price-note {
    padding: 15px 20px;
    background: #f8f9fa;
    text-align: center;
}

.price-note p {
    margin: 0;
    font-size: 0.85rem;
    color: #666;
}

.price-cta {
    text-align: center;
    margin-top: 20px;
}

.know-more-btn {
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.know-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, #f4d03f, #d4af37);
}

.know-more-btn:active {
    transform: translateY(0);
}

/* EMI Calculator Section */
.emi-calculator-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.emi-header {
    background: #d4af37;
    padding: 15px 20px;
    text-align: center;
}

.emi-header h3 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.emi-calculator {
    padding: 30px 20px;
}

.calculator-field {
    margin-bottom: 25px;
}

.calculator-field label {
    display: block;
    font-size: 0.85rem;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.amount-input {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
}

.currency-symbol {
    font-weight: 600;
    margin-right: 8px;
    color: #333;
}

.amount-input input {
    border: none;
    outline: none;
    font-size: 1rem;
    font-weight: 600;
    flex: 1;
}

.slider-container, .duration-container, .interest-container {
    position: relative;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    margin-bottom: 10px;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #d4af37;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #d4af37;
    cursor: pointer;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #666;
}

.slider-value, .duration-selected, .interest-value {
    font-weight: 600;
    color: #333;
}

.duration-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #666;
}

.interest-value {
    text-align: center;
    font-size: 0.9rem;
    margin-top: 5px;
}

.emi-result {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    text-align: center;
    margin-top: 20px;
}

.result-label {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 8px;
    text-transform: uppercase;
    font-weight: 600;
}

.result-amount {
    font-size: 1.8rem;
    font-weight: 700;
    color: #d4af37;
}

.result-currency {
    font-size: 0.9rem;
    color: #666;
    font-weight: normal;
}

/* Amenities Section */
.amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.amenity-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.amenity-item img {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.amenity-item p {
    font-weight: 500;
    color: #333;
}

.amenities-banner {
    position: relative;
    text-align: center;
    border-radius: 8px;
    overflow: hidden;
}

.amenities-banner img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.banner-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Gallery Section */
.gallery {
    background: #f8f9fa;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.gallery-grid img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

.view-gallery-btn {
    display: block;
    margin: 0 auto;
    padding: 12px 30px;
    background: #d4af37;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-transform: uppercase;
}

/* Downloads Section */
.download-item {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
}

.download-item h6 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.download-item p {
    color: #666;
    margin-bottom: 20px;
}

.download-item img {
    max-width: 100%;
    border-radius: 8px;
}

/* Product Kit Download Styling */
.product-kit-download {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.product-kit-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-kit-download:hover h6 {
    color: #d4af37;
}

.product-kit-download:before {
    content: "📥 Click to Download";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #d4af37;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-kit-download:hover:before {
    opacity: 1;
}

/* Contact Section */
.contact {
    background: #f8f9fa;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto 40px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.phone-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.country-select {
    flex: 0 0 150px;
}

.flag-display {
    flex: 0 0 30px;
}

.flag-display img {
    width: 24px;
    height: 16px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-group input {
    width: auto;
    margin: 0;
}

.send-btn {
    background: #d4af37;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-transform: uppercase;
    display: block;
    margin: 0 auto;
}

.contact-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.contact-btn {
    padding: 12px 25px;
    background: #d4af37;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-transform: uppercase;
    display: inline-block;
}

/* Disclaimer Section */
.disclaimer-section {
    background: #f8f9fa;
    padding: 80px 0;
    border-top: 1px solid #e9ecef;
}

.disclaimer-header {
    text-align: center;
    margin-bottom: 5px;
}

.disclaimer-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 700;
}

.disclaimer-content {
    max-width: 1000px;
    margin: 0 auto;
}

.disclaimer-text {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    line-height: 1.8;
}

.disclaimer-text h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    margin: 30px 0 15px 0;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.disclaimer-text h3:first-child {
    margin-top: 0;
}

.disclaimer-text p {
    color: #555;
    margin-bottom: 20px;
    text-align: justify;
    font-size: 0.95rem;
}

.disclaimer-note {
    background: #e8f4fd;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    margin-top: 30px;
    font-style: italic;
}

.disclaimer-note strong {
    color: #2c3e50;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 60px 0 20px;
}

/* Footer Media - Explore More Section */
.footer-media {
    margin: 30px 0;
    text-align: left;
}

.footer-media h4 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: white;
    font-weight: 600;
}

.footer-media a {
    display: inline-block;
    margin: 0 20px;
    padding: 15px;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.footer-media a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.footer-media a:hover::before {
    transform: translateX(100%);
}

.footer-media a:first-of-type {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}

.footer-media a:first-of-type:hover {
    background: linear-gradient(135deg, #ee5a24, #ff6b6b);
    transform: translateY(-8px) scale(1.1);
    box-shadow: 0 15px 35px rgba(255, 107, 107, 0.6);
}

.footer-media a:last-of-type {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    box-shadow: 0 8px 20px rgba(255, 0, 0, 0.4);
}

.footer-media a:last-of-type:hover {
    background: linear-gradient(135deg, #cc0000, #ff0000);
    transform: translateY(-8px) scale(1.1);
    box-shadow: 0 15px 35px rgba(255, 0, 0, 0.6);
}

.footer-media a i {
    font-size: 28px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.footer-media a:hover i {
    transform: rotate(360deg) scale(1.2);
    text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-main h5,
.footer-section h5 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-main p {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.footer-actions a,
.footer-actions button {
    padding: 8px 16px;
    background: #d4af37;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    text-transform: uppercase;
}

.app-links {
    display: flex;
    gap: 10px;
}

.app-links img {
    height: 40px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    font-size: 14px;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-social h4 {
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.social-links a {
    color: #ccc;
    text-decoration: none;
    font-size: 14px;
}

.social-links a:hover {
    color: white;
}

.app-links-mobile {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.app-links-mobile img {
    height: 40px;
}

.footer-bottom {
    padding-top: 20px;
    border-top: 1px solid #555;
}

.footer-logos-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-left,
.footer-center,
.footer-right {
    flex: 1;
    display: flex;
    align-items: center;
}

.footer-left {
    justify-content: flex-start;
}

.footer-center {
    justify-content: center;
}

.footer-right {
    justify-content: flex-end;
}

.footer-logos {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.godrej-logo img {
    height: 35px;
    transition: all 0.3s ease;
}

.godrej-logo img:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

.local-logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.local-logo {
    height: 50px;
    width: auto;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.local-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.5);
}

.rera-number {
    color: #d4af37;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-align: center;
    background: rgba(212, 175, 55, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.footer-copyright {
    text-align: center;
}

.footer-copyright p {
    margin: 0;
    font-size: 14px;
    color: #ccc;
    font-weight: 500;
}

.footer-links {
    display: flex;
    gap: 15px;
}

.footer-links a {
    color: #ccc;
    text-decoration: none;
    font-size: 12px;
}

.footer-links a:hover {
    color: white;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 1002;
    position: relative;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.mobile-menu-toggle:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.05);
}

.mobile-menu-toggle span {
    width: 28px;
    height: 3px;
    background-color: white;
    margin: 4px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
    display: block;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile Navigation Below Logo */
.mobile-nav-below {
    display: none;
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
}

.mobile-nav-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 10px 0;
}

.mobile-nav-scroll::-webkit-scrollbar {
    display: none;
}

.mobile-nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 20px;
    white-space: nowrap;
    min-width: max-content;
}

.mobile-nav-menu a {
    text-decoration: none;
    color: #fff;
    font-weight: 500;
    padding: 8px 16px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-radius: 15px;
    background: transparent;
    border: none;
    white-space: nowrap;
}

.mobile-nav-menu a:hover {
    background: transparent;
    color: #d4af37;
}

/* Zoom Controls */
.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.zoom-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.zoom-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.zoom-btn:active {
    transform: scale(0.95);
}

/* Tablet Design */
@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        padding: 0 30px;
    }

    .hero-content h1 {
        font-size: 3.5rem;
    }

    .property-details {
        flex-wrap: wrap;
        gap: 15px;
    }

    .action-buttons {
        flex-wrap: wrap;
        gap: 10px;
    }

    .action-btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .amenities-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .price-content {
        gap: 30px;
    }

    /* Hero Intro Tablet */
    .hero-intro-content h1 {
        font-size: 2.8rem;
    }

    .hero-intro-content h2 {
        font-size: 2.2rem;
    }

    .hero-tagline {
        font-size: 1.2rem;
    }

    .hero-cta-buttons {
        gap: 15px;
    }

    /* Key Highlights Tablet */
    .highlights-header h2 {
        font-size: 2.4rem;
    }

    .highlights-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    /* Residences Tablet */
    .residences-header h2 {
        font-size: 2.4rem;
    }

    .residences-header h3 {
        font-size: 1.6rem;
    }

    .residence-info {
        grid-template-columns: repeat(3, 1fr); /* Keep 3 columns on tablet */
        gap: 20px;
    }

    /* Location Advantage Tablet */
    .location-header h2 {
        font-size: 2.4rem;
    }

    .location-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Trust Section Tablet */
    .trust-content h2 {
        font-size: 2.4rem;
    }

    /* Final CTA Tablet */
    .final-cta-content h2 {
        font-size: 2.6rem;
    }

    .cta-grid {
        flex-wrap: nowrap;
        gap: 25px;
    }

    /* Ensure header containers are properly contained on tablet */
    .overview-header,
    .neighbourhood-header,
    .plans-header,
    .price-header,
    .amenities-header,
    .gallery-header,
    .downloads-header,
    .contact-header {
        padding: 0 20px;
        box-sizing: border-box;
    }

    .overview-header h2,
    .neighbourhood-header h2,
    .plans-header h2,
    .price-header h2,
    .amenities-header h2,
    .gallery-header h2,
    .downloads-header h2,
    .contact-header h2 {
        padding: 0 20px;
    }
}

/* Small Tablet Design */
@media (max-width: 900px) and (min-width: 769px) {
    .cta-grid {
        flex-wrap: wrap;
        justify-content: center;
        gap: 25px;
    }

    .cta-card {
        flex: 0 1 calc(50% - 12.5px);
        min-width: 250px;
    }

    .residence-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .info-item {
        padding: 20px 15px;
    }
}

/* Mobile Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
        max-width: 100%;
    }

    /* Mobile Navigation */
    .mobile-menu-toggle {
        display: none; /* Hide hamburger menu */
    }

    .sub-nav-menu {
        display: none; /* Hide desktop menu */
    }

    .mobile-nav-below {
        display: block; /* Show mobile navigation below logo */
    }

    .godrej-logo h3 {
        font-size: 16px !important;
        white-space: nowrap;
    }

    /* Hero Section Mobile */
    .hero {
        height: 100vh;
        min-height: 600px;
        max-height: 800px;
    }

    .hero-content h1 {
        font-size: 2.2rem;
        line-height: 1.2;
        margin-bottom: 15px;
    }

    .hero-content .location {
        font-size: 16px;
        margin-bottom: 20px;
    }

    .property-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 25px;
    }

    .property-details span {
        font-size: 14px;
    }

    .separator {
        display: none;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
        gap: 12px;
    }

    .action-btn {
        padding: 15px 20px;
        font-size: 14px;
        text-align: center;
        width: 100%;
    }

    /* Overview Section Mobile */
    .overview {
        padding: 40px 0;
    }

    .overview .container {
        padding: 0 15px;
    }

    .overview-text h3 {
        font-size: 1.4rem;
    }

    .overview-text p {
        font-size: 13px;
        line-height: 1.5;
    }

    .overview-image img {
        height: 250px;
        object-fit: cover;
    }

    /* Hero Intro Mobile */
    .hero-intro {
        padding: 40px 0;
    }

    .hero-intro-content h1 {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .hero-intro-content h2 {
        font-size: 1.4rem;
        line-height: 1.4;
    }

    .hero-tagline {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .hero-description p {
        font-size: 1rem;
        line-height: 1.6;
    }

    .hero-cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
        padding: 12px 20px;
        font-size: 0.9rem;
    }

    /* Key Highlights Mobile */
    .key-highlights {
        padding: 40px 0;
    }

    .highlights-header h2 {
        font-size: 2rem;
        margin-bottom: 5px;
    }

    .highlights-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 40px;
    }

    .highlight-item {
        padding: 25px 20px;
    }

    .highlight-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .highlight-item h4 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .highlights-cta {
        flex-direction: column;
        align-items: center;
    }

    /* Residences Mobile */
    .residences-section {
        padding: 40px 0;
    }

    .residences-header h2 {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .residences-header h3 {
        font-size: 1.4rem;
    }

    .residence-info {
        grid-template-columns: 1fr; /* Stack on mobile only */
        gap: 20px;
        margin-bottom: 30px;
    }

    .info-item {
        padding: 20px;
    }

    .residence-description {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .residences-cta {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .residences-cta .cta-btn {
        flex: none;
        min-width: 280px;
        max-width: 100%;
    }

    /* Location Advantage Mobile */
    .location-advantage {
        padding: 60px 0;
    }

    .location-header h2 {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .location-header p {
        font-size: 1.1rem;
    }

    .location-grid {
        grid-template-columns: 1fr;
        gap: 25px;
        margin-bottom: 40px;
    }

    .location-card {
        padding: 30px 20px;
    }

    .location-card h4 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .location-card li,
    .location-card p {
        font-size: 1rem;
    }

    .location-cta {
        flex-direction: column;
        align-items: center;
    }

    /* Trust Section Mobile */
    .trust-section {
        padding: 60px 0;
    }

    .trust-content h2 {
        font-size: 2rem;
        margin-bottom: 25px;
    }

    .trust-content p {
        font-size: 13px;
        line-height: 1.6;
    }

    /* Final CTA Mobile */
    .final-cta-section {
        padding: 60px 0;
    }

    .final-cta-content h2 {
        font-size: 2.2rem;
        margin-bottom: 40px;
    }

    .cta-grid {
        flex-direction: column;
        gap: 20px;
        margin-bottom: 40px;
    }

    .cta-card {
        flex: none;
        min-width: auto;
        max-width: 100%;
    }

    .cta-card {
        padding: 20px 18px;
    }

    .cta-icon {
        font-size: 1.6rem;
        margin-bottom: 12px;
    }

    .cta-card h4 {
        font-size: 1.1rem;
        margin-bottom: 6px;
    }

    .cta-card p {
        font-size: 0.85rem;
    }

    .urgency-banner {
        padding: 20px 15px;
    }

    .urgency-banner p {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }

    /* Section Headers Mobile */
    .overview-header h2,
    .neighbourhood-header h2,
    .plans-header h2,
    .price-header h2,
    .amenities-header h2,
    .gallery-header h2,
    .downloads-header h2,
    .contact-header h2 {
        font-size: 1.1rem;
        margin-bottom: 20px;
        padding: 0 15px;
    }

    /* Hide decorative lines on mobile to prevent overflow */
    .overview-header h2:before,
    .overview-header h2:after,
    .neighbourhood-header h2:before,
    .neighbourhood-header h2:after,
    .plans-header h2:before,
    .plans-header h2:after,
    .price-header h2:before,
    .price-header h2:after,
    .amenities-header h2:before,
    .amenities-header h2:after,
    .gallery-header h2:before,
    .gallery-header h2:after,
    .downloads-header h2:before,
    .downloads-header h2:after,
    .contact-header h2:before,
    .contact-header h2:after {
        display: none;
    }

    /* Ensure header containers are properly contained */
    .overview-header,
    .neighbourhood-header,
    .plans-header,
    .price-header,
    .amenities-header,
    .gallery-header,
    .downloads-header,
    .contact-header {
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        padding: 0 15px;
        box-sizing: border-box;
        margin-left: auto;
        margin-right: auto;
    }

    /* Ensure all sections are properly contained */
    section {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    /* Location Section Mobile */
    .neighbourhood {
        padding: 40px 0;
    }

    .section-desc {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 25px;
        padding: 0 15px;
    }

    .map-container {
        margin: 0 15px;
        border-radius: 8px;
        overflow: hidden;
    }

    #google-map {
        height: 300px !important;
    }

    .location-highlights {
        position: static;
        transform: none;
        max-width: none;
        margin-top: 15px;
        left: auto;
        top: auto;
        padding: 0;
    }

    .location-card {
        margin: 0;
        padding: 20px 15px;
        border-radius: 8px;
    }

    .location-header-left {
        font-size: 11px;
        margin-bottom: 8px;
    }

    .location-header {
        margin-bottom: 15px;
    }

    .location-header div:first-child {
        font-size: 14px;
        font-weight: 600;
    }

    .location-item {
        padding: 8px 0;
        margin-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .location-item p {
        font-size: 13px;
        line-height: 1.4;
    }

    .location-icon {
        font-size: 16px;
        margin-right: 10px;
    }

    .fullscreen-btn {
        width: 35px;
        height: 35px;
        bottom: 15px;
        right: 15px;
    }

    /* Price Section Mobile */
    .price {
        padding: 40px 0;
    }

    .price-content {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 0 15px;
    }

    .price-table-section,
    .emi-calculator-section {
        padding: 20px 15px;
        border-radius: 8px;
    }

    .price-table-header h3,
    .emi-header h3 {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .price-row {
        padding: 15px 0;
        font-size: 14px;
    }

    .calculator-field {
        margin-bottom: 20px;
    }

    .calculator-field label {
        font-size: 12px;
        margin-bottom: 8px;
    }

    .amount-input input {
        font-size: 16px;
        padding: 12px;
    }

    .know-more-btn {
        padding: 10px 25px;
        font-size: 12px;
    }

    /* Amenities Section Mobile */
    .amenities {
        padding: 30px 0;
    }

    .amenities-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        padding: 0 15px;
    }

    .amenity-item {
        text-align: center;
        padding: 15px 10px;
    }

    .amenity-item img {
        width: 50px;
        height: 50px;
        margin-bottom: 10px;
    }

    .amenity-item p {
        font-size: 12px;
        line-height: 1.3;
    }

    .amenities-banner {
        margin: 30px 15px 0;
        border-radius: 8px;
        overflow: hidden;
    }

    /* Gallery Section Mobile */
    .gallery {
        padding: 30px 0;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        padding: 0 15px;
        margin-bottom: 25px;
    }

    .gallery-grid img {
        border-radius: 6px;
        height: 150px;
        object-fit: cover;
    }

    .view-gallery-btn {
        margin: 0 15px;
        padding: 15px 30px;
        font-size: 14px;
    }

    /* Downloads Section Mobile */
    .downloads {
        padding: 40px 0;
    }

    .download-item {
        padding: 0 15px;
        text-align: center;
    }

    .download-item h6 {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .download-item p {
        font-size: 12px;
        margin-bottom: 15px;
    }

    .download-item img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
    }

    /* Product Kit Mobile Styling */
    .product-kit-download:before {
        font-size: 10px;
        padding: 3px 8px;
        top: 5px;
        right: 5px;
    }

    /* Contact Section Mobile */
    .contact {
        padding: 40px 0;
    }

    .contact .container {
        padding: 0 15px;
    }

    .contact p {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 25px;
    }

    .contact-form {
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .form-group input,
    .form-group select {
        padding: 12px 15px;
        font-size: 16px;
        border-radius: 6px;
    }

    .phone-input {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .country-select {
        order: 1;
    }

    .flag-display {
        order: 2;
        align-self: flex-start;
        margin: 10px 0;
    }

    .phone-input input[type="tel"] {
        order: 3;
    }

    .send-btn {
        width: 100%;
        padding: 15px;
        font-size: 16px;
        margin-bottom: 20px;
    }

    .contact-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .contact-btn {
        padding: 15px 20px;
        font-size: 14px;
        text-align: center;
    }

    /* Footer Mobile */
    .footer {
        padding: 40px 0 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 0 15px;
    }

    .footer-main {
        order: 1;
    }

    .footer-section {
        text-align: left;
    }

    .footer-section h5 {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .footer-section ul li {
        margin-bottom: 8px;
    }

    .footer-section ul li a {
        font-size: 14px;
    }

    .footer-bottom {
        padding: 15px 10px 0;
    }

    .footer-logos-row {
        gap: 15px;
    }

    .godrej-logo img {
        height: 25px;
    }

    .local-logo {
        height: 35px;
    }

    .rera-number {
        font-size: 11px;
        padding: 3px 6px;
    }

    .footer-copyright p {
        font-size: 11px;
    }
}

/* Enquiry Modal Styles */
.enquiry-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.enquiry-modal.show {
    display: flex;
    animation: modalFadeIn 0.3s ease-out;
}

.enquiry-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.enquiry-modal-content {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: url('./images/hero-image.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 16px;
    max-width: 450px;
    width: 100%;
    max-height: 95vh; /* Increased height */
    min-height: 600px; /* Ensure more height for content */
    overflow: visible; /* Remove scroll */
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.enquiry-modal-header {
    position: relative;
    padding: 20px 25px 15px;
    text-align: center;
    background: rgba(255, 255, 255, 0.5);
    color: #333;
    border-radius: 16px 16px 0 0;
    border-bottom: 2px solid #d4af37;
    flex-shrink: 0;
    z-index: 1;
    backdrop-filter: blur(2px);
}

.enquiry-logo {
    margin-bottom: 15px;
    animation: logoFadeIn 0.6s ease-out 0.2s both;
}

.enquiry-logo img {
    height: 40px;
    width: auto;
    max-width: 180px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.enquiry-modal-header h3 {
    font-size: 1.05rem; /* Reduce heading size */
    font-weight: 700;
    margin: 0 0 5px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #333;
}

.enquiry-modal-header p {
    font-size: 0.82rem; /* Reduce subheading size */
    margin: 0;
    color: #666;
    line-height: 1.4;
}

.enquiry-close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #e1e5e9;
    color: #666;
    font-size: 20px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    font-weight: bold;
    z-index: 10;
}

.enquiry-close-btn:hover {
    background: #f8f9fa;
    border-color: #d4af37;
    color: #d4af37;
    transform: scale(1.1);
}

.enquiry-form {
    padding: 12px 18px; /* Reduce padding */
    font-size: 0.92rem; /* Slightly smaller text */
    background: rgba(255, 255, 255, 0.5);
    margin: 0;
    flex: 1;
    overflow-y: auto;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(2px);
}

.enquiry-form-group {
    margin-bottom: 15px;
}

.enquiry-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
    font-size: 0.85rem; /* Reduce label size */
}

.enquiry-form-group input,
.enquiry-form-group textarea {
    width: 100%;
    padding: 7px 10px; /* Reduce input/textarea padding */
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 0.88rem; /* Reduce input/textarea font size */
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    font-family: inherit;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

.enquiry-form-group input:focus,
.enquiry-form-group textarea:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.phone-input-wrapper {
    display: flex;
    align-items: center;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    overflow: hidden;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

.phone-input-wrapper:focus-within {
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.country-code {
    background: #f8f9fa;
    padding: 12px 15px;
    font-weight: 600;
    color: #666;
    border-right: 1px solid #e1e5e9;
    font-size: 0.95rem;
}

.phone-input-wrapper input {
    border: none;
    flex: 1;
    padding: 10px 12px;
    font-size: 0.9rem;
    box-sizing: border-box;
}

.phone-input-wrapper input:focus {
    outline: none;
    box-shadow: none;
}

.enquiry-form-group.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 20px;
}

.enquiry-form-group.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    margin-top: 2px;
}

.enquiry-form-group.checkbox-group label {
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
    color: #666;
    font-weight: 400;
}

.enquiry-submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 0.95rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.enquiry-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.enquiry-submit-btn:active {
    transform: translateY(0);
}

.enquiry-contact-info {
    padding: 15px 25px 20px;
    text-align: center;
    border-top: 1px solid #e1e5e9;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 0 0 16px 16px;
    flex-shrink: 0;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(2px);
}

.enquiry-contact-info p {
    margin: 0;
    font-size: 0.85rem; /* Reduce contact info size */
    color: #666;
}

.enquiry-contact-info a {
    color: #d4af37;
    text-decoration: none;
    font-weight: 600;
}

.enquiry-contact-info a:hover {
    text-decoration: underline;
}

/* Modal Animations */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes logoFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsive for Enquiry Modal */
@media (max-width: 768px) {
    .enquiry-modal {
        padding: 10px;
        align-items: flex-start;
        padding-top: 20px;
    }

    .enquiry-modal-content {
        max-width: 100%;
        width: calc(100% - 20px);
        max-height: 90vh;
        border-radius: 12px;
    }

    .enquiry-modal-header {
        padding: 15px 20px 10px;
        border-radius: 12px 12px 0 0;
    }

    .enquiry-close-btn {
        top: 8px;
        right: 12px;
        width: 28px;
        height: 28px;
        font-size: 18px;
    }

    .enquiry-logo {
        margin-bottom: 10px;
    }

    .enquiry-logo img {
        height: 35px;
        max-width: 140px;
    }

    .enquiry-modal-header h3 {
        font-size: 1.1rem;
        margin-bottom: 3px;
    }

    .enquiry-modal-header p {
        font-size: 0.85rem;
    }

    .enquiry-form {
        padding: 15px 20px;
    }

    .enquiry-form-group {
        margin-bottom: 12px;
    }

    .enquiry-form-group label {
        font-size: 0.85rem;
        margin-bottom: 4px;
    }

    .enquiry-form-group input,
    .enquiry-form-group textarea {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .enquiry-form-group textarea {
        min-height: 60px;
    }

    .enquiry-submit-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .enquiry-contact-info {
        padding: 12px 20px 15px;
    }

    .enquiry-contact-info p {
        font-size: 0.8rem;
    }

    .country-code {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .phone-input-wrapper input {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .enquiry-form-group.checkbox-group {
        margin-bottom: 15px;
    }

    .enquiry-form-group.checkbox-group label {
        font-size: 0.8rem;
        line-height: 1.3;
    }
}

/* Additional Premium Responsive Improvements */
@media (max-width: 768px) {
    .overview-header h2,
    .plans-header h2,
    .price-header h2,
    .amenities-header h2,
    .gallery-header h2,
    .downloads-header h2,
    .contact-header h2,
    .certificates-header h2,
    .neighbourhood-header h2 {
        font-size: 1.2rem;
        letter-spacing: 2px;
        padding: 0 25px;
    }

    .overview-header h2:before,
    .overview-header h2:after,
    .plans-header h2:before,
    .plans-header h2:after,
    .price-header h2:before,
    .price-header h2:after,
    .amenities-header h2:before,
    .amenities-header h2:after,
    .gallery-header h2:before,
    .gallery-header h2:after,
    .downloads-header h2:before,
    .downloads-header h2:after,
    .contact-header h2:before,
    .contact-header h2:after,
    .certificates-header h2:before,
    .certificates-header h2:after,
    .neighbourhood-header h2:before,
    .neighbourhood-header h2:after {
        width: 50px;
        margin-left: 25px;
        margin-right: 25px;
    }

    .hero-intro-content h1 {
        font-size: 2rem;
    }

    .hero-intro-content h2 {
        font-size: 1.6rem;
    }

    .cta-btn {
        padding: 15px 25px;
        font-size: 1rem;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .hero-cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
}
