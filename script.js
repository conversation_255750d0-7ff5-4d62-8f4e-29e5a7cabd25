// Godrej Park World - Simple JavaScript

document.addEventListener('DOMContentLoaded', function() {

    // Enquiry Modal Functionality
    const enquiryModal = document.getElementById('enquiryModal');
    const enquiryCloseBtn = document.getElementById('enquiryCloseBtn');
    const enquiryForm = document.getElementById('enquiryForm');
    const enquiryModalOverlay = document.querySelector('.enquiry-modal-overlay');

    // Check if popup should be shown (not shown in last 24 hours)
    function shouldShowPopup() {
        const lastShown = localStorage.getItem('enquiryPopupLastShown');
        const popupDismissed = localStorage.getItem('enquiryPopupDismissed');

        // For testing/demo purposes, always show popup on page load
        // Comment out the lines below if you want to enable the 24-hour restriction
        return true;

        // Uncomment the lines below to enable 24-hour restriction
        /*
        if (popupDismissed === 'true') {
            return false;
        }

        if (!lastShown) {
            return true;
        }

        const now = new Date().getTime();
        const lastShownTime = parseInt(lastShown);
        const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        return (now - lastShownTime) > twentyFourHours;
        */
    }

    // Show enquiry popup
    function showEnquiryPopup() {
        if (enquiryModal && shouldShowPopup()) {
            setTimeout(() => {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
                localStorage.setItem('enquiryPopupLastShown', new Date().getTime().toString());
            }, 1000); // Show popup after 1 second
        }
    }

    // Close enquiry popup
    function closeEnquiryPopup() {
        if (enquiryModal) {
            enquiryModal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // Manual function to show popup (for testing or manual trigger)
    window.showEnquiryPopup = function() {
        if (enquiryModal) {
            enquiryModal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    };

    // Function to open enquiry modal (used by Book Your Unit button)
    window.openEnquiryModal = function() {
        if (enquiryModal) {
            enquiryModal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    };

    // Function to reset popup state (for testing)
    window.resetEnquiryPopup = function() {
        localStorage.removeItem('enquiryPopupLastShown');
        localStorage.removeItem('enquiryPopupDismissed');
        console.log('Enquiry popup state reset. Refresh the page to see the popup again.');
    };

    // Hide enquiry popup
    function hideEnquiryPopup() {
        if (enquiryModal) {
            enquiryModal.classList.remove('show');
            document.body.style.overflow = 'auto';
        }
    }

    // Close popup and mark as dismissed
    function dismissEnquiryPopup() {
        hideEnquiryPopup();
        // For demo purposes, don't permanently dismiss
        // Uncomment the line below if you want to permanently dismiss after closing
        // localStorage.setItem('enquiryPopupDismissed', 'true');
    }

    // Event listeners for closing popup
    if (enquiryCloseBtn) {
        enquiryCloseBtn.addEventListener('click', dismissEnquiryPopup);
    }

    if (enquiryModalOverlay) {
        enquiryModalOverlay.addEventListener('click', function(e) {
            if (e.target === enquiryModalOverlay) {
                closeEnquiryPopup();
            }
        });
    }

    // Close popup with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && enquiryModal && enquiryModal.classList.contains('show')) {
            dismissEnquiryPopup();
        }
    });

    // Handle enquiry form submission
    if (enquiryForm) {
        enquiryForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const name = formData.get('name');
            const phone = formData.get('phone');
            const email = formData.get('email');
            const message = formData.get('message');
            const updates = formData.get('updates');

            // Basic validation
            if (!name || !phone || !email) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Phone number validation (10 digits)
            const phoneRegex = /^[0-9]{10}$/;
            if (!phoneRegex.test(phone)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Phone Number',
                    text: 'Please enter a valid 10-digit mobile number.',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Email',
                    text: 'Please enter a valid email address.',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('.enquiry-submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare data for sending
            const enquiryData = {
                name: name,
                phone: phone,
                email: email,
                message: message || '',
                updates: updates ? true : false
            };

            // Send data to PHP script
            fetch('send_enquiry_simple.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(enquiryData)
            })
            .then(response => {
                console.log('Response status:', response.status); // Debug log
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response received:', data); // Debug log
                if (data.success === true) {
                    console.log('Success confirmed - data saved, closing form'); // Debug log

                    // Store form reference before closing
                    const formElement = enquiryForm;

                    // Close the form immediately
                    hideEnquiryPopup();

                    // Reset the form
                    if (formElement) {
                        formElement.reset();
                    }

                    // Check if there's a pending download
                    const pendingDownload = sessionStorage.getItem('pendingDownload');

                    // Add a small delay to ensure form is closed before showing alert
                    setTimeout(() => {
                        console.log('Showing success alert - data was saved successfully'); // Debug log

                        if (pendingDownload) {
                            // Parse download info and trigger download
                            const downloadInfo = JSON.parse(pendingDownload);

                            // Clear the pending download
                            sessionStorage.removeItem('pendingDownload');

                            // Show success message with download info
                            Swal.fire({
                                icon: 'success',
                                title: 'Thank You!',
                                text: `Your enquiry has been sent successfully! Your ${downloadInfo.bhkType} PDF is now downloading.`,
                                confirmButtonColor: '#d4af37',
                                confirmButtonText: 'Great!',
                                timer: 5000,
                                timerProgressBar: true
                            });

                            // Trigger the download
                            setTimeout(() => {
                                const link = document.createElement('a');
                                link.href = downloadInfo.pdfUrl;
                                link.download = downloadInfo.filename;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            }, 1000);

                        } else {
                            // Regular success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Thank You!',
                                text: 'Your enquiry has been sent successfully! Our team will contact you shortly.',
                                confirmButtonColor: '#d4af37',
                                confirmButtonText: 'Great!',
                                timer: 5000,
                                timerProgressBar: true
                            });
                        }
                    }, 100);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops!',
                        text: data.message || 'Sorry, there was an error sending your enquiry.',
                        confirmButtonColor: '#d4af37',
                        confirmButtonText: 'Try Again',
                        footer: '<a href="tel:+918287682878" style="color: #d4af37; text-decoration: none;">📞 Or call us directly at +91 82876 82878</a>'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Connection Error',
                    text: 'Unable to send your enquiry. Please check your internet connection and try again.',
                    confirmButtonColor: '#d4af37',
                    confirmButtonText: 'Retry',
                    footer: '<a href="tel:+918287682878" style="color: #d4af37; text-decoration: none;">📞 Or call us directly at +91 82876 82878</a>'
                });
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // Clear any existing popup restrictions for demo purposes
    localStorage.removeItem('enquiryPopupDismissed');

    // Initialize popup on page load
    showEnquiryPopup();

    // PDF Download - Show Enquiry Popup First
    const downloadButtons = document.querySelectorAll('.download-btn');
    downloadButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();

            const pdfUrl = this.getAttribute('data-pdf');
            const filename = this.getAttribute('data-filename');
            const bhkType = this.closest('.pdf-thumbnail-item, .certificate-item').querySelector('h4').textContent;

            // Store download info for after form submission
            sessionStorage.setItem('pendingDownload', JSON.stringify({
                pdfUrl: pdfUrl,
                filename: filename,
                bhkType: bhkType
            }));

            // Show enquiry popup
            showEnquiryPopup();
        });
    });

    // PDF Thumbnail Click - Show Enquiry Popup
    const pdfThumbnails = document.querySelectorAll('.pdf-thumbnail');
    pdfThumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const downloadBtn = this.closest('.pdf-thumbnail-item').querySelector('.download-btn');
            const bhkType = this.closest('.pdf-thumbnail-item').querySelector('h4').textContent;
            const pdfUrl = downloadBtn.getAttribute('data-pdf');
            const filename = downloadBtn.getAttribute('data-filename');

            // Store download info for after form submission
            sessionStorage.setItem('pendingDownload', JSON.stringify({
                pdfUrl: pdfUrl,
                filename: filename,
                bhkType: bhkType
            }));

            // Show enquiry popup
            showEnquiryPopup();
        });
    });

    // Product Kit Download - Show Enquiry Popup First
    const productKitDownload = document.querySelector('.product-kit-download');
    if (productKitDownload) {
        productKitDownload.addEventListener('click', function(e) {
            e.preventDefault();

            const pdfUrl = this.getAttribute('data-pdf');
            const filename = this.getAttribute('data-filename');
            const itemType = 'Product Kit';

            // Store download info for after form submission
            sessionStorage.setItem('pendingDownload', JSON.stringify({
                pdfUrl: pdfUrl,
                filename: filename,
                bhkType: itemType
            }));

            // Show enquiry popup
            showEnquiryPopup();
        });
    }

    // Mobile Navigation - No toggle needed (always visible below logo on mobile)

    // Smooth Scrolling for Sub Navigation
    const subNavLinks = document.querySelectorAll('.sub-nav-menu a');
    subNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Handle contact form submission
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const name = formData.get('name');
            const phone = formData.get('phone');
            const email = formData.get('email');
            const country = formData.get('country');
            const updates = formData.get('updates');

            // Basic validation
            if (!name || !phone || !email) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields.',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Email',
                    text: 'Please enter a valid email address.',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Phone validation (basic - at least 10 digits)
            if (phone.length < 10) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Invalid Phone Number',
                    text: 'Please enter a valid phone number (at least 10 digits).',
                    confirmButtonColor: '#d4af37'
                });
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('.send-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare data for sending
            const contactData = {
                name: name,
                phone: phone,
                email: email,
                country: country || 'India',
                updates: updates ? true : false
            };

            // Send data to PHP script
            fetch('send_contact_simple.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reset form immediately
                    this.reset();

                    // Then show success alert
                    Swal.fire({
                        icon: 'success',
                        title: 'Message Sent!',
                        text: 'Thank you for contacting us! We will get back to you soon.',
                        confirmButtonColor: '#d4af37',
                        confirmButtonText: 'Great!',
                        timer: 5000,
                        timerProgressBar: true
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops!',
                        text: data.message || 'Sorry, there was an error sending your message.',
                        confirmButtonColor: '#d4af37',
                        confirmButtonText: 'Try Again',
                        footer: '<a href="tel:+918287682878" style="color: #d4af37; text-decoration: none;">📞 Or call us directly at +91 82876 82878</a>'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Connection Error',
                    text: 'Unable to send your message. Please check your internet connection and try again.',
                    confirmButtonColor: '#d4af37',
                    confirmButtonText: 'Retry',
                    footer: '<a href="tel:+918287682878" style="color: #d4af37; text-decoration: none;">📞 Or call us directly at +91 82876 82878</a>'
                });
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // EMI Calculator Sliders
    const loanAmountInput = document.getElementById('loanAmount');
    const monthlySlider = document.getElementById('monthlySlider');
    const durationSlider = document.getElementById('durationSlider');
    const interestSlider = document.getElementById('interestSlider');

    const monthlySliderValue = document.querySelector('.slider-value');
    const durationValue = document.querySelector('.duration-selected');
    const interestValue = document.querySelector('.interest-value');
    const emiResult = document.querySelector('.result-amount');

    function calculateEMI() {
        const loanAmount = parseFloat(loanAmountInput.value.replace(/,/g, '')) * 1000; // Convert to actual amount
        const monthlyPaymentPercent = parseFloat(monthlySlider.value);
        const durationYears = parseFloat(durationSlider.value);
        const interestRate = parseFloat(interestSlider.value);

        // Calculate EMI using standard formula
        const monthlyRate = interestRate / (12 * 100);
        const numberOfPayments = durationYears * 12;

        let emi = 0;
        if (monthlyRate > 0) {
            emi = (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) /
                  (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
        } else {
            emi = loanAmount / numberOfPayments;
        }

        // Update result display
        if (emiResult) {
            emiResult.innerHTML = `₹${Math.round(emi).toLocaleString()} <span class="result-currency">/ month</span>`;
        }
    }

    function updateSliderValues() {
        if (monthlySlider && monthlySliderValue) {
            monthlySlider.addEventListener('input', function() {
                monthlySliderValue.textContent = this.value + '%';
                calculateEMI();
            });
        }

        if (durationSlider && durationValue) {
            durationSlider.addEventListener('input', function() {
                durationValue.textContent = this.value + ' Years';
                calculateEMI();
            });
        }

        if (interestSlider && interestValue) {
            interestSlider.addEventListener('input', function() {
                interestValue.textContent = this.value + '%';
                calculateEMI();
            });
        }

        if (loanAmountInput) {
            loanAmountInput.addEventListener('input', function() {
                calculateEMI();
            });
        }

        // Initial calculation
        calculateEMI();
    }

    updateSliderValues();

    // Action Buttons
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(btn => {
        if (btn.textContent.includes('Share')) {
            btn.addEventListener('click', function() {
                if (navigator.share) {
                    navigator.share({
                        title: 'Godrej Park World',
                        text: 'Godrej Park World - Hinjawadi Phase 1, Pune',
                        url: window.location.href
                    });
                } else {
                    // Fallback
                    const url = window.location.href;
                    navigator.clipboard.writeText(url).then(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'Link Copied!',
                            text: 'Website link has been copied to clipboard',
                            confirmButtonColor: '#d4af37',
                            timer: 2000,
                            timerProgressBar: true
                        });
                    });
                }
            });
        }
        
        if (btn.textContent.includes('Schedule')) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                if (enquiryModal) {
                    enquiryModal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            });
        }
        
        if (btn.textContent.includes('Contact')) {
            btn.addEventListener('click', function() {
                if (enquiryModal) {
                    enquiryModal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            });
        }
        
        if (btn.textContent.includes('EMI Calculator')) {
            btn.addEventListener('click', function() {
                const priceSection = document.getElementById('price');
                if (priceSection) {
                    priceSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            });
        }
    });

    // Plan Image Interaction
    const planOverlay = document.querySelector('.plan-overlay');
    if (planOverlay) {
        planOverlay.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Interactive Plan',
                text: 'Click on the hotspots in the master plan to explore different areas and amenities.',
                confirmButtonColor: '#d4af37',
                confirmButtonText: 'Got it!'
            });
        });
    }

    // Interactive Plans Section
    const planImageWrapper = document.querySelector('.plan-image-wrapper');
    const masterPlanImage = document.querySelector('.master-plan-image');
    const interactOverlay = document.querySelector('.interact-overlay');
    const scrollTopBtn = document.querySelector('.scroll-top-btn');
    const planHotspots = document.querySelectorAll('.plan-hotspot');

    // Interact overlay click
    if (interactOverlay) {
        interactOverlay.addEventListener('click', function() {
            // Toggle hotspots visibility
            planHotspots.forEach(hotspot => {
                hotspot.style.display = hotspot.style.display === 'none' ? 'block' : 'none';
            });

            // Update overlay text
            const interactText = this.querySelector('.interact-text');
            if (interactText) {
                const isHidden = planHotspots[0] && planHotspots[0].style.display === 'none';
                interactText.textContent = isHidden ? 'Click to Interact' : 'Hide Hotspots';
            }
        });
    }

    // Hotspot interactions
    planHotspots.forEach(hotspot => {
        hotspot.addEventListener('click', function() {
            const info = this.getAttribute('data-info');
            const label = this.querySelector('.hotspot-label');

            // Show detailed info (you can customize this)
            Swal.fire({
                icon: 'info',
                title: info,
                text: `Learn more about ${info} and its features. Contact us for detailed information and site visit.`,
                confirmButtonColor: '#d4af37',
                confirmButtonText: 'Contact Us',
                showCancelButton: true,
                cancelButtonText: 'Close'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Open enquiry modal
                    const enquiryModal = document.getElementById('enquiry-modal');
                    if (enquiryModal) {
                        enquiryModal.classList.add('show');
                        document.body.style.overflow = 'hidden';
                    }
                }
            });
        });

        // Enhanced hover effect
        hotspot.addEventListener('mouseenter', function() {
            const marker = this.querySelector('.hotspot-marker');
            if (marker) {
                marker.style.transform = 'scale(1.3)';
                marker.style.background = '#ff6b35';
            }
        });

        hotspot.addEventListener('mouseleave', function() {
            const marker = this.querySelector('.hotspot-marker');
            if (marker) {
                marker.style.transform = 'scale(1)';
                marker.style.background = '#d4af37';
            }
        });
    });

    // Scroll to top functionality
    if (scrollTopBtn) {
        scrollTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Simple click functionality for plan image
    if (masterPlanImage) {
        masterPlanImage.addEventListener('click', function(e) {
            // Open fullscreen view when clicking on the plan image
            openFullscreen(this, e);
        });
    }

    // Godrej Properties Logo
    const godrejLogo = document.querySelector('.godrej-logo');
    if (godrejLogo) {
        godrejLogo.addEventListener('click', function() {
            window.location.href = '/';
        });
    }

    // Contact Number Click
    const contactNumber = document.querySelector('.contact-number');
    if (contactNumber) {
        contactNumber.addEventListener('click', function() {
            Swal.fire({
                icon: 'info',
                title: 'Contact Options',
                html: `
                    <div style="text-align: left; margin: 20px 0;">
                        <p><strong>📞 Call:</strong> <a href="tel:+918287682878" style="color: #d4af37;">+91 82876 82878</a></p>
                        <p><strong>📧 Email:</strong> <a href="mailto:<EMAIL>" style="color: #d4af37;"><EMAIL></a></p>
                        <p><strong>🏢 Office:</strong> Hinjawadi Phase 1, Pune</p>
                        <p><strong>⏰ Timing:</strong> 9:00 AM - 7:00 PM</p>
                    </div>
                `,
                confirmButtonColor: '#d4af37',
                confirmButtonText: 'Call Now',
                showCancelButton: true,
                cancelButtonText: 'Close'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = 'tel:+918287682878';
                }
            });
        });
    }

    // Navigation Buttons
    const enquireBtn = document.querySelector('.enquire-btn');
    const scheduleBtn = document.querySelector('.schedule-btn');
    const scheduleVisitBtn = document.getElementById('scheduleVisitBtn');

    if (enquireBtn) {
        enquireBtn.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    if (scheduleBtn) {
        scheduleBtn.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    // Fixed Schedule Visit Button
    if (scheduleVisitBtn) {
        scheduleVisitBtn.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    // Price Table Know More Button
    const priceKnowMoreBtn = document.getElementById('priceKnowMoreBtn');
    if (priceKnowMoreBtn) {
        priceKnowMoreBtn.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    // Hero Schedule Visit Button
    const scheduleVisitHero = document.querySelector('.schedule-visit-hero');
    if (scheduleVisitHero) {
        scheduleVisitHero.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    // CTA Schedule Visit Button
    const scheduleVisitCta = document.querySelector('.schedule-visit-cta');
    if (scheduleVisitCta) {
        scheduleVisitCta.addEventListener('click', function() {
            if (enquiryModal) {
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        });
    }

    // Book Now CTA Button - Multiple selectors to ensure it works
    const bookNowCta = document.querySelector('.book-now-cta') || document.querySelector('button.cta-card');
    console.log('Book Now button found:', bookNowCta);

    // Also try to find by text content
    const allButtons = document.querySelectorAll('button');
    let bookNowButton = null;
    allButtons.forEach(btn => {
        if (btn.textContent && btn.textContent.includes('Book Now')) {
            bookNowButton = btn;
            console.log('Found Book Now button by text:', btn);
        }
    });

    const finalBookNowBtn = bookNowCta || bookNowButton;

    if (finalBookNowBtn) {
        finalBookNowBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Book Now button clicked!');
            if (enquiryModal) {
                console.log('Opening enquiry modal');
                enquiryModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                console.log('Enquiry modal not found');
            }
        });
        console.log('Event listener added to Book Now button');
    } else {
        console.log('Book Now button not found with any method');
    }

    // Initialize default values are now handled by calculateEMI() function

    // Scroll effect for sub-nav with background
    window.addEventListener('scroll', function() {
        const subNav = document.getElementById('subNav');
        const scrollPosition = window.scrollY;

        if (scrollPosition > 100) {
            subNav.classList.add('scrolled');
        } else {
            subNav.classList.remove('scrolled');
        }
    });



    // Location button functionality
    const locationButton = document.querySelector('.location-button');
    if (locationButton) {
        locationButton.addEventListener('click', function() {
            // Open Google Maps with the location
            const address = "Hinjawadi Phase 1, Pune, Maharashtra, India";
            const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
            window.open(googleMapsUrl, '_blank');
        });
    }

    // Auto show popup every 25 seconds if not already visible
    function startAutoPopup() {
        setInterval(() => {
            if (enquiryModal && !enquiryModal.classList.contains('show')) {
                showEnquiryPopup();
            }
        }, 25000); // 25 seconds
    }

    // Start the auto popup timer
    startAutoPopup();
});

// Facebook Pixel tracking (placeholder)
if (typeof fbq !== 'undefined') {
    fbq('track', 'PageView');
}

// Fullscreen functionality for map
function toggleFullscreen() {
    const mapContainer = document.querySelector('.map-container');

    if (!document.fullscreenElement) {
        mapContainer.requestFullscreen().catch(err => {
            console.log(`Error attempting to enable fullscreen: ${err.message}`);
        });
    } else {
        document.exitFullscreen();
    }
}

// Function to open directions
function openDirections() {
    // Open Google Maps with directions to the location
    const address = "Hinjawadi Phase 1, Pune, Maharashtra, India";
    const encodedAddress = encodeURIComponent(address);
    const mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
    window.open(mapsUrl, '_blank');
}

// Function to open image in fullscreen
function openFullscreen(img, event) {
    // Create fullscreen overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    `;

    // Create fullscreen image
    const fullscreenImg = document.createElement('img');
    fullscreenImg.src = img.src;
    fullscreenImg.alt = img.alt;
    let currentZoom = 1;
    fullscreenImg.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        transition: transform 0.3s ease;
        cursor: grab;
        transform: scale(${currentZoom});
    `;

    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 30px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 40px;
        cursor: pointer;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s ease;
    `;

    // Close button hover effect
    closeBtn.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(255, 255, 255, 0.3)';
    });
    closeBtn.addEventListener('mouseleave', function() {
        this.style.background = 'rgba(255, 255, 255, 0.2)';
    });

    // Create zoom in button
    const zoomInBtn = document.createElement('button');
    zoomInBtn.innerHTML = '+';
    zoomInBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 110px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 30px;
        cursor: pointer;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s ease;
        font-weight: bold;
    `;

    // Create zoom out button
    const zoomOutBtn = document.createElement('button');
    zoomOutBtn.innerHTML = '−';
    zoomOutBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 170px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-size: 30px;
        cursor: pointer;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s ease;
        font-weight: bold;
    `;

    // Zoom functionality
    function updateZoom() {
        fullscreenImg.style.transform = `scale(${currentZoom})`;
    }

    zoomInBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        if (currentZoom < 3) {
            currentZoom += 0.25;
            updateZoom();
        }
    });

    zoomOutBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        if (currentZoom > 0.5) {
            currentZoom -= 0.25;
            updateZoom();
        }
    });

    // Hover effects for zoom buttons
    [zoomInBtn, zoomOutBtn].forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255, 255, 255, 0.3)';
        });
        btn.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.2)';
        });
    });

    // Add elements to overlay
    overlay.appendChild(fullscreenImg);
    overlay.appendChild(closeBtn);
    overlay.appendChild(zoomInBtn);
    overlay.appendChild(zoomOutBtn);

    // Close functionality
    function closeFullscreen() {
        document.body.removeChild(overlay);
        document.body.style.overflow = 'auto';
    }

    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeFullscreen();
        }
    });

    closeBtn.addEventListener('click', closeFullscreen);

    // ESC key to close
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeFullscreen();
        }
    });

    // Add to body and prevent scrolling
    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';
}

// Zoom functionality for master plan image
let planImageZoom = 1;
let planImageOffsetY = 0;
const minZoom = 0.5;
const maxZoom = 3;
const zoomStep = 0.25;
const moveStep = 20;

function zoomIn() {
    const planImage = document.querySelector('.master-plan-image');
    if (planImage && planImageZoom < maxZoom) {
        planImageZoom += zoomStep;
        applyPlanImageZoom();
    }
}

function zoomOut() {
    const planImage = document.querySelector('.master-plan-image');
    if (planImage && planImageZoom > minZoom) {
        planImageZoom -= zoomStep;
        applyPlanImageZoom();
    }
}

function applyPlanImageZoom() {
    const planImage = document.querySelector('.master-plan-image');
    if (planImage) {
        planImage.style.transform = `scale(${planImageZoom})`;

        // Make the image wrapper scrollable if zoomed
        const imageWrapper = planImage.closest('.plan-image-wrapper');
        if (imageWrapper) {
            if (planImageZoom > 1) {
                imageWrapper.style.overflow = 'auto';
                imageWrapper.style.cursor = 'grab';
                imageWrapper.classList.add('zoomed');
            } else {
                imageWrapper.style.overflow = 'hidden';
                imageWrapper.style.cursor = 'default';
                imageWrapper.classList.remove('zoomed');
            }
        }
    }
}
