<?php
// Simple test to verify forms are working
echo "<h1>Form Testing Page</h1>";

// Test contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_contact'])) {
    echo "<h2>Contact Form Test Result:</h2>";
    
    $contactData = [
        'name' => $_POST['name'] ?? '',
        'phone' => $_POST['phone'] ?? '',
        'email' => $_POST['email'] ?? '',
        'country' => $_POST['country'] ?? 'India',
        'updates' => isset($_POST['updates']) ? 'Yes' : 'No'
    ];
    
    echo "<pre>";
    print_r($contactData);
    echo "</pre>";
    
    // Test the actual contact handler
    echo "<h3>Testing send_contact_simple.php:</h3>";
    
    $testData = json_encode($contactData);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/send_contact_simple.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    echo "<p><strong>Response:</strong> $response</p>";
}

// Test enquiry form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_enquiry'])) {
    echo "<h2>Enquiry Form Test Result:</h2>";
    
    $enquiryData = [
        'name' => $_POST['name'] ?? '',
        'phone' => $_POST['phone'] ?? '',
        'email' => $_POST['email'] ?? '',
        'message' => $_POST['message'] ?? '',
        'updates' => isset($_POST['updates']) ? 'Yes' : 'No'
    ];
    
    echo "<pre>";
    print_r($enquiryData);
    echo "</pre>";
    
    // Test the actual enquiry handler
    echo "<h3>Testing send_enquiry_simple.php:</h3>";
    
    $testData = json_encode($enquiryData);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/send_enquiry_simple.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $testData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    echo "<p><strong>Response:</strong> $response</p>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Testing - Godrej Property</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-container { background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h2>Test Contact Form</h2>
    <div class="form-container">
        <form method="POST">
            <div class="form-group">
                <label>Name *</label>
                <input type="text" name="name" required value="John Doe">
            </div>
            <div class="form-group">
                <label>Phone *</label>
                <input type="tel" name="phone" required value="9876543210">
            </div>
            <div class="form-group">
                <label>Email *</label>
                <input type="email" name="email" required value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Country</label>
                <select name="country">
                    <option value="India" selected>India</option>
                    <option value="USA">USA</option>
                    <option value="UK">UK</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="updates" checked> 
                    Receive updates and promotions
                </label>
            </div>
            <button type="submit" name="test_contact">Test Contact Form</button>
        </form>
    </div>

    <h2>Test Enquiry Form</h2>
    <div class="form-container">
        <form method="POST">
            <div class="form-group">
                <label>Name *</label>
                <input type="text" name="name" required value="Jane Smith">
            </div>
            <div class="form-group">
                <label>Phone *</label>
                <input type="tel" name="phone" required value="9876543211">
            </div>
            <div class="form-group">
                <label>Email *</label>
                <input type="email" name="email" required value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Message</label>
                <textarea name="message" rows="4">I am interested in 2BHK apartments. Please provide more details about pricing and availability.</textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="updates" checked> 
                    Receive updates and promotions
                </label>
            </div>
            <button type="submit" name="test_enquiry">Test Enquiry Form</button>
        </form>
    </div>

    <h2>Quick Links</h2>
    <ul>
        <li><a href="index.html">Main Website</a></li>
        <li><a href="setup_database.php">Setup Database</a></li>
        <li><a href="admin/">Admin Panel</a></li>
    </ul>

    <h2>Instructions</h2>
    <ol>
        <li><strong>Start XAMPP:</strong> Make sure Apache and MySQL services are running in XAMPP</li>
        <li><strong>Setup Database:</strong> Visit <a href="setup_database.php">setup_database.php</a> to create the database</li>
        <li><strong>Test Forms:</strong> Use the forms above to test submissions</li>
        <li><strong>Access Admin:</strong> Go to <a href="admin/">admin panel</a> (username: admin, password: admin123)</li>
    </ol>
</body>
</html>
