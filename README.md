# Godrej Property Contact & Enquiry Management System

A complete PHP-based system for managing contact us and enquiry form submissions with an admin panel.

## Features

- ✅ Contact Us form handling
- ✅ Enquiry form handling  
- ✅ Database storage with MySQL
- ✅ Admin panel with authentication
- ✅ Status management (New, Contacted, Follow-up, Closed)
- ✅ Follow-up date scheduling
- ✅ Search and filter functionality
- ✅ Responsive design
- ✅ Real-time statistics

## Setup Instructions

### 1. Prerequisites
- XAMPP installed and running
- Apache and MySQL services started

### 2. Database Setup
1. Open your browser and go to `http://localhost/godrejproperty/setup_database.php`
2. This will automatically:
   - Create the database `godrej_property`
   - Create required tables (`contact_us`, `enquiry`, `admin_users`)
   - Create default admin user

### 3. Admin Access
- URL: `http://localhost/godrejproperty/admin/`
- Username: `admin`
- Password: `admin123`

### 4. Test Forms
1. Visit `http://localhost/godrejproperty/` to see your website
2. Fill out the contact form or enquiry popup
3. Check the admin panel to see submissions

## File Structure

```
godrejproperty/
├── config/
│   └── database.php          # Database configuration
├── admin/
│   ├── index.php            # Admin dashboard
│   ├── login.php            # Admin login
│   ├── contacts.php         # Contact management
│   ├── enquiries.php        # Enquiry management
│   ├── auth.php             # Authentication functions
│   └── admin-styles.css     # Admin panel styles
├── setup_database.php       # Database setup script
├── send_contact_simple.php  # Contact form handler
├── send_enquiry_simple.php  # Enquiry form handler
├── index.html              # Main website
├── script.js               # Website JavaScript
└── styles.css              # Website styles
```

## Database Tables

### contact_us
- id, name, phone, email, country, updates
- status, follow_up_date, notes
- created_at, updated_at

### enquiry  
- id, name, phone, email, message, updates
- status, follow_up_date, notes
- created_at, updated_at

### admin_users
- id, username, password, email, full_name
- is_active, last_login, created_at, updated_at

## Admin Panel Features

### Dashboard
- Statistics overview
- Recent submissions
- Quick access to all sections

### Contact Management
- View all contact submissions
- Update status and add notes
- Schedule follow-ups
- Search and filter
- Direct call/email links

### Enquiry Management
- View all enquiry submissions
- Update status and add notes
- Schedule follow-ups
- Search and filter
- View customer messages

## Status Workflow

1. **New** - Fresh submission, requires attention
2. **Contacted** - Customer has been contacted
3. **Follow-up** - Requires follow-up action (with date)
4. **Closed** - Case completed

## Security Features

- Password hashing for admin users
- SQL injection protection with prepared statements
- Session-based authentication
- Input validation and sanitization
- CSRF protection ready

## Customization

### Change Admin Credentials
1. Go to admin panel
2. Or directly update in database
3. Or modify `setup_database.php` before running

### Add More Admin Users
Use the admin_users table to add more administrators.

### Modify Form Fields
Update the HTML forms and corresponding PHP handlers as needed.

## Troubleshooting

### Database Connection Issues
- Ensure XAMPP MySQL is running
- Check database credentials in `config/database.php`
- Verify database exists by running setup script

### Forms Not Working
- Check browser console for JavaScript errors
- Verify PHP files are accessible
- Check server error logs

### Admin Panel Access Issues
- Clear browser cache and cookies
- Try incognito/private browsing mode
- Check if session is working

## Support

For any issues or customizations, check:
1. Browser console for JavaScript errors
2. PHP error logs in XAMPP
3. Database connection status
4. File permissions

## Next Steps

Consider adding:
- Email notifications for new submissions
- Export functionality (CSV/Excel)
- Advanced reporting and analytics
- SMS integration
- Automated follow-up reminders
