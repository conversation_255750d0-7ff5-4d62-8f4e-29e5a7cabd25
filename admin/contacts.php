<?php
require_once 'auth.php';
requireLogin();
require_once '../config/database.php';

$pdo = getDBConnection();
if (!$pdo) {
    die("Database connection failed");
}

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $id = (int)$_POST['id'];
    $action = $_POST['action'];

    // Debug: Log the received data
    error_log("Contact Update - ID: $id, Action: $action, Status: " . ($_POST['status'] ?? 'not set'));

    try {
        if ($action === 'update_status') {
            // Validate that we have a valid ID
            if ($id <= 0) {
                throw new Exception("Invalid contact ID: $id");
            }

            $status = $_POST['status'];
            $notes = $_POST['notes'] ?? '';
            $followUpDate = $_POST['follow_up_date'] ?? null;

            // Convert empty string to null for date field
            if ($followUpDate === '') {
                $followUpDate = null;
            }

            $sql = "UPDATE contact_us SET status = ?, notes = ?, follow_up_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$status, $notes, $followUpDate, $id]);

            // Check if the update actually affected any rows
            $rowsAffected = $stmt->rowCount();
            error_log("Contact Update - Rows affected: $rowsAffected");

            if ($rowsAffected > 0) {
                $message = "Contact updated successfully!";
            } else {
                $error = "No contact found with ID: $id";
            }
        }
    } catch (PDOException $e) {
        $error = "Error updating contact: " . $e->getMessage();
        error_log("Contact Update Error: " . $e->getMessage());
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Contact Update Error: " . $e->getMessage());
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$searchTerm = $_GET['search'] ?? '';

// Build query
$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($searchTerm) {
    $whereConditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

$whereClause = $whereConditions ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Get contacts
try {
    $sql = "SELECT * FROM contact_us $whereClause ORDER BY created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $contacts = $stmt->fetchAll();
    
    // Get statistics
    $statsSQL = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
        SUM(CASE WHEN status = 'contacted' THEN 1 ELSE 0 END) as contacted_count,
        SUM(CASE WHEN status = 'follow_up' THEN 1 ELSE 0 END) as follow_up_count,
        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
    FROM contact_us";
    $stats = $pdo->query($statsSQL)->fetch();
    
} catch (PDOException $e) {
    $error = "Error fetching contacts: " . $e->getMessage();
    $contacts = [];
    $stats = ['total' => 0, 'new_count' => 0, 'contacted_count' => 0, 'follow_up_count' => 0, 'closed_count' => 0];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-ENZMLBXKNW"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-ENZMLBXKNW');
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Management - Godrej Property</title>
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>Godrej Admin</h3>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="index.php">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li class="active">
                    <a href="contacts.php">
                        <i class="fas fa-address-book"></i>
                        Contact Us
                        <?php if ($stats['new_count'] > 0): ?>
                            <span class="badge"><?php echo $stats['new_count']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="enquiries.php">
                        <i class="fas fa-question-circle"></i>
                        Enquiries
                    </a>
                </li>
                <li>
                    <a href="?logout=1">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="main-header">
                <h1>Contact Management</h1>
                <div class="user-info">
                    <span>Total: <?php echo $stats['total']; ?> contacts</span>
                </div>
            </header>

            <?php if (isset($message)): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon contact">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['new_count']; ?></h3>
                        <p>New Contacts</p>
                        <small>Requires attention</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon enquiry">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['contacted_count']; ?></h3>
                        <p>Contacted</p>
                        <small>In progress</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon follow-up">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['follow_up_count']; ?></h3>
                        <p>Follow-up</p>
                        <small>Scheduled</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon closed">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['closed_count']; ?></h3>
                        <p>Closed</p>
                        <small>Completed</small>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="content-section">
                <form method="GET" class="filters">
                    <input type="text" name="search" placeholder="Search by name, email, or phone..." 
                           value="<?php echo htmlspecialchars($searchTerm); ?>">
                    
                    <select name="status">
                        <option value="">All Status</option>
                        <option value="new" <?php echo $statusFilter === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="contacted" <?php echo $statusFilter === 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                        <option value="follow_up" <?php echo $statusFilter === 'follow_up' ? 'selected' : ''; ?>>Follow-up</option>
                        <option value="closed" <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    </select>
                    
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="contacts.php" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <!-- Contacts Table -->
            <div class="content-section">
                <h2>Contact Submissions (<?php echo count($contacts); ?>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Country</th>
                                <th>Status</th>
                                <th>Follow-up</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contacts as $contact): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($contact['name']); ?></td>
                                <td>
                                    <a href="tel:<?php echo htmlspecialchars($contact['phone']); ?>">
                                        <?php echo htmlspecialchars($contact['phone']); ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>">
                                        <?php echo htmlspecialchars($contact['email']); ?>
                                    </a>
                                </td>
                                <td><?php echo htmlspecialchars($contact['country']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $contact['status']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $contact['status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($contact['follow_up_date']): ?>
                                        <?php echo date('M j, Y', strtotime($contact['follow_up_date'])); ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M j, Y H:i', strtotime($contact['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary" onclick="openUpdateModal(<?php echo htmlspecialchars(json_encode($contact)); ?>)">
                                            Update
                                        </button>
                                        <a href="tel:<?php echo htmlspecialchars($contact['phone']); ?>" class="btn btn-sm btn-success">
                                            Call
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($contacts)): ?>
                            <tr>
                                <td colspan="8" class="text-center">No contacts found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Modal -->
    <div id="updateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Update Contact</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="id" id="contactId">
                
                <div class="form-group">
                    <label>Contact Name</label>
                    <input type="text" id="contactName" readonly>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Phone</label>
                        <input type="text" id="contactPhone" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="contactEmail" readonly>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="status">Status *</label>
                    <select name="status" id="status" required>
                        <option value="contacted">Contacted</option>
                        <option value="follow_up">Follow-up Required</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="follow_up_date">Follow-up Date</label>
                    <input type="date" name="follow_up_date" id="follow_up_date">
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea name="notes" id="notes" rows="4" placeholder="Add your notes here..."></textarea>
                </div>
                
                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary">Update Contact</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openUpdateModal(contact) {
            document.getElementById('contactId').value = contact.id;
            document.getElementById('contactName').value = contact.name;
            document.getElementById('contactPhone').value = contact.phone;
            document.getElementById('contactEmail').value = contact.email;

            // If status is 'new', default to 'contacted', otherwise use current status
            var statusValue = contact.status === 'new' ? 'contacted' : contact.status;
            document.getElementById('status').value = statusValue;

            document.getElementById('follow_up_date').value = contact.follow_up_date || '';
            document.getElementById('notes').value = contact.notes || '';

            document.getElementById('updateModal').classList.add('show');
        }
        
        function closeModal() {
            document.getElementById('updateModal').classList.remove('show');
        }
        
        // Close modal when clicking outside
        document.getElementById('updateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
