<?php
session_start();
require_once '../config/database.php';

// Function to check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// Function to require login
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

// Function to login user
function loginUser($username, $password) {
    $pdo = getDBConnection();
    if (!$pdo) {
        return false;
    }
    
    try {
        $sql = "SELECT id, username, password, full_name, email FROM admin_users WHERE username = ? AND is_active = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$username]);
        
        if ($stmt->rowCount() === 1) {
            $user = $stmt->fetch();
            
            if (password_verify($password, $user['password'])) {
                // Update last login
                $updateSQL = "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?";
                $updateStmt = $pdo->prepare($updateSQL);
                $updateStmt->execute([$user['id']]);
                
                // Set session variables
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $user['id'];
                $_SESSION['admin_username'] = $user['username'];
                $_SESSION['admin_name'] = $user['full_name'];
                $_SESSION['admin_email'] = $user['email'];
                
                return true;
            }
        }
        
        return false;
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

// Function to logout user
function logoutUser() {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Handle logout request
if (isset($_GET['logout'])) {
    logoutUser();
}
?>
