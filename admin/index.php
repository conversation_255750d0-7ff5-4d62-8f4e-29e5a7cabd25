<?php
require_once 'auth.php';
requireLogin();
require_once '../config/database.php';

$pdo = getDBConnection();
if (!$pdo) {
    die("Database connection failed");
}

// Get statistics
try {
    // Contact Us statistics
    $contactStats = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
            SUM(CASE WHEN status = 'contacted' THEN 1 ELSE 0 END) as contacted_count,
            SUM(CASE WHEN status = 'follow_up' THEN 1 ELSE 0 END) as follow_up_count,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
        FROM contact_us
    ")->fetch();
    
    // Enquiry statistics
    $enquiryStats = $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
            SUM(CASE WHEN status = 'contacted' THEN 1 ELSE 0 END) as contacted_count,
            SUM(CASE WHEN status = 'follow_up' THEN 1 ELSE 0 END) as follow_up_count,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
        FROM enquiry
    ")->fetch();
    
    // Recent submissions (last 10)
    $recentContacts = $pdo->query("
        SELECT 'contact' as type, id, name, phone, email, status, created_at 
        FROM contact_us 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll();
    
    $recentEnquiries = $pdo->query("
        SELECT 'enquiry' as type, id, name, phone, email, status, created_at 
        FROM enquiry 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll();
    
    // Combine and sort recent submissions
    $recentSubmissions = array_merge($recentContacts, $recentEnquiries);
    usort($recentSubmissions, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    $recentSubmissions = array_slice($recentSubmissions, 0, 10);
    
} catch (PDOException $e) {
    error_log("Dashboard query error: " . $e->getMessage());
    $contactStats = ['total' => 0, 'new_count' => 0, 'contacted_count' => 0, 'follow_up_count' => 0, 'closed_count' => 0];
    $enquiryStats = ['total' => 0, 'new_count' => 0, 'contacted_count' => 0, 'follow_up_count' => 0, 'closed_count' => 0];
    $recentSubmissions = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Godrej Property</title>
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>Godrej Admin</h3>
            </div>
            <ul class="sidebar-menu">
                <li class="active">
                    <a href="index.php">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="contacts.php">
                        <i class="fas fa-address-book"></i>
                        Contact Us
                        <?php if ($contactStats['new_count'] > 0): ?>
                            <span class="badge"><?php echo $contactStats['new_count']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="enquiries.php">
                        <i class="fas fa-question-circle"></i>
                        Enquiries
                        <?php if ($enquiryStats['new_count'] > 0): ?>
                            <span class="badge"><?php echo $enquiryStats['new_count']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="?logout=1">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="main-header">
                <h1>Dashboard</h1>
                <div class="user-info">
                    <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_name']); ?></span>
                </div>
            </header>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon contact">
                        <i class="fas fa-address-book"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $contactStats['total']; ?></h3>
                        <p>Total Contacts</p>
                        <small><?php echo $contactStats['new_count']; ?> new</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon enquiry">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $enquiryStats['total']; ?></h3>
                        <p>Total Enquiries</p>
                        <small><?php echo $enquiryStats['new_count']; ?> new</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon follow-up">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $contactStats['follow_up_count'] + $enquiryStats['follow_up_count']; ?></h3>
                        <p>Follow-ups Pending</p>
                        <small>Requires attention</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon closed">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $contactStats['closed_count'] + $enquiryStats['closed_count']; ?></h3>
                        <p>Closed Cases</p>
                        <small>Completed</small>
                    </div>
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="content-section">
                <h2>Recent Submissions</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentSubmissions as $submission): ?>
                            <tr>
                                <td>
                                    <span class="type-badge <?php echo $submission['type']; ?>">
                                        <?php echo ucfirst($submission['type']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($submission['name']); ?></td>
                                <td><?php echo htmlspecialchars($submission['phone']); ?></td>
                                <td><?php echo htmlspecialchars($submission['email']); ?></td>
                                <td>
                                    <span class="status-badge <?php echo $submission['status']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $submission['status'])); ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y H:i', strtotime($submission['created_at'])); ?></td>
                                <td>
                                    <a href="<?php echo $submission['type'] === 'contact' ? 'contacts.php' : 'enquiries.php'; ?>?id=<?php echo $submission['id']; ?>" 
                                       class="btn btn-sm btn-primary">View</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($recentSubmissions)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No submissions yet</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
