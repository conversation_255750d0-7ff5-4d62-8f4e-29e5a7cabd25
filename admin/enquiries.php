<?php
require_once 'auth.php';
requireLogin();
require_once '../config/database.php';

$pdo = getDBConnection();
if (!$pdo) {
    die("Database connection failed");
}

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $id = (int)$_POST['id'];
    $action = $_POST['action'];

    // Debug: Log the received data
    error_log("Enquiry Update - ID: $id, Action: $action, Status: " . ($_POST['status'] ?? 'not set'));

    try {
        if ($action === 'update_status') {
            // Validate that we have a valid ID
            if ($id <= 0) {
                throw new Exception("Invalid enquiry ID: $id");
            }

            $status = $_POST['status'];
            $notes = $_POST['notes'] ?? '';
            $followUpDate = $_POST['follow_up_date'] ?? null;

            // Convert empty string to null for date field
            if ($followUpDate === '') {
                $followUpDate = null;
            }

            $sql = "UPDATE enquiry SET status = ?, notes = ?, follow_up_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$status, $notes, $followUpDate, $id]);

            // Check if the update actually affected any rows
            $rowsAffected = $stmt->rowCount();
            error_log("Enquiry Update - Rows affected: $rowsAffected");

            if ($rowsAffected > 0) {
                $message = "Enquiry updated successfully!";
            } else {
                $error = "No enquiry found with ID: $id";
            }
        }
    } catch (PDOException $e) {
        $error = "Error updating enquiry: " . $e->getMessage();
        error_log("Enquiry Update Error: " . $e->getMessage());
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Enquiry Update Error: " . $e->getMessage());
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$searchTerm = $_GET['search'] ?? '';

// Build query
$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($searchTerm) {
    $whereConditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ? OR message LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

$whereClause = $whereConditions ? "WHERE " . implode(" AND ", $whereConditions) : "";

// Get enquiries
try {
    $sql = "SELECT * FROM enquiry $whereClause ORDER BY created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $enquiries = $stmt->fetchAll();
    
    // Get statistics
    $statsSQL = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new_count,
        SUM(CASE WHEN status = 'contacted' THEN 1 ELSE 0 END) as contacted_count,
        SUM(CASE WHEN status = 'follow_up' THEN 1 ELSE 0 END) as follow_up_count,
        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_count
    FROM enquiry";
    $stats = $pdo->query($statsSQL)->fetch();
    
} catch (PDOException $e) {
    $error = "Error fetching enquiries: " . $e->getMessage();
    $enquiries = [];
    $stats = ['total' => 0, 'new_count' => 0, 'contacted_count' => 0, 'follow_up_count' => 0, 'closed_count' => 0];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enquiry Management - Godrej Property</title>
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>Godrej Admin</h3>
            </div>
            <ul class="sidebar-menu">
                <li>
                    <a href="index.php">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="contacts.php">
                        <i class="fas fa-address-book"></i>
                        Contact Us
                    </a>
                </li>
                <li class="active">
                    <a href="enquiries.php">
                        <i class="fas fa-question-circle"></i>
                        Enquiries
                        <?php if ($stats['new_count'] > 0): ?>
                            <span class="badge"><?php echo $stats['new_count']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="?logout=1">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="main-header">
                <h1>Enquiry Management</h1>
                <div class="user-info">
                    <span>Total: <?php echo $stats['total']; ?> enquiries</span>
                </div>
            </header>

            <?php if (isset($message)): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon contact">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['new_count']; ?></h3>
                        <p>New Enquiries</p>
                        <small>Requires attention</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon enquiry">
                        <i class="fas fa-phone-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['contacted_count']; ?></h3>
                        <p>Contacted</p>
                        <small>In progress</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon follow-up">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['follow_up_count']; ?></h3>
                        <p>Follow-up</p>
                        <small>Scheduled</small>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon closed">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo $stats['closed_count']; ?></h3>
                        <p>Closed</p>
                        <small>Completed</small>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="content-section">
                <form method="GET" class="filters">
                    <input type="text" name="search" placeholder="Search by name, email, phone, or message..." 
                           value="<?php echo htmlspecialchars($searchTerm); ?>">
                    
                    <select name="status">
                        <option value="">All Status</option>
                        <option value="new" <?php echo $statusFilter === 'new' ? 'selected' : ''; ?>>New</option>
                        <option value="contacted" <?php echo $statusFilter === 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                        <option value="follow_up" <?php echo $statusFilter === 'follow_up' ? 'selected' : ''; ?>>Follow-up</option>
                        <option value="closed" <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                    </select>
                    
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="enquiries.php" class="btn btn-secondary">Clear</a>
                </form>
            </div>

            <!-- Enquiries Table -->
            <div class="content-section">
                <h2>Enquiry Submissions (<?php echo count($enquiries); ?>)</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Message</th>
                                <th>Status</th>
                                <th>Follow-up</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($enquiries as $enquiry): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($enquiry['name']); ?></td>
                                <td>
                                    <a href="tel:<?php echo htmlspecialchars($enquiry['phone']); ?>">
                                        <?php echo htmlspecialchars($enquiry['phone']); ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="mailto:<?php echo htmlspecialchars($enquiry['email']); ?>">
                                        <?php echo htmlspecialchars($enquiry['email']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($enquiry['message']): ?>
                                        <span title="<?php echo htmlspecialchars($enquiry['message']); ?>">
                                            <?php echo htmlspecialchars(substr($enquiry['message'], 0, 50)); ?>
                                            <?php if (strlen($enquiry['message']) > 50): ?>...<?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <em>No message</em>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $enquiry['status']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $enquiry['status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($enquiry['follow_up_date']): ?>
                                        <?php echo date('M j, Y', strtotime($enquiry['follow_up_date'])); ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M j, Y H:i', strtotime($enquiry['created_at'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary" onclick="openUpdateModal(<?php echo htmlspecialchars(json_encode($enquiry)); ?>)">
                                            Update
                                        </button>
                                        <a href="tel:<?php echo htmlspecialchars($enquiry['phone']); ?>" class="btn btn-sm btn-success">
                                            Call
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            
                            <?php if (empty($enquiries)): ?>
                            <tr>
                                <td colspan="8" class="text-center">No enquiries found</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Modal -->
    <div id="updateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Update Enquiry</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="id" id="enquiryId">
                
                <div class="form-group">
                    <label>Customer Name</label>
                    <input type="text" id="enquiryName" readonly>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>Phone</label>
                        <input type="text" id="enquiryPhone" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="enquiryEmail" readonly>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Customer Message</label>
                    <textarea id="enquiryMessage" readonly rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="status">Status *</label>
                    <select name="status" id="status" required>
                        <option value="contacted">Contacted</option>
                        <option value="follow_up">Follow-up Required</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="follow_up_date">Follow-up Date</label>
                    <input type="date" name="follow_up_date" id="follow_up_date">
                </div>
                
                <div class="form-group">
                    <label for="notes">Internal Notes</label>
                    <textarea name="notes" id="notes" rows="4" placeholder="Add your notes here..."></textarea>
                </div>
                
                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary">Update Enquiry</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openUpdateModal(enquiry) {
            document.getElementById('enquiryId').value = enquiry.id;
            document.getElementById('enquiryName').value = enquiry.name;
            document.getElementById('enquiryPhone').value = enquiry.phone;
            document.getElementById('enquiryEmail').value = enquiry.email;
            document.getElementById('enquiryMessage').value = enquiry.message || 'No message provided';

            // If status is 'new', default to 'contacted', otherwise use current status
            var statusValue = enquiry.status === 'new' ? 'contacted' : enquiry.status;
            document.getElementById('status').value = statusValue;

            document.getElementById('follow_up_date').value = enquiry.follow_up_date || '';
            document.getElementById('notes').value = enquiry.notes || '';

            document.getElementById('updateModal').classList.add('show');
        }
        
        function closeModal() {
            document.getElementById('updateModal').classList.remove('show');
        }
        
        // Close modal when clicking outside
        document.getElementById('updateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
